<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محادثة مع ويليكس الذكي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        
        .status-bar {
            background: #f8f9fa;
            padding: 10px 20px;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
            color: #6c757d;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-left: 10px;
        }
        
        .message.bot .message-content {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            margin-right: 10px;
        }
        
        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 16px;
        }
        
        .user-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .bot-avatar {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 10px;
        }
        
        .input-field {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }
        
        .input-field:focus {
            border-color: #4facfe;
        }
        
        .send-button {
            padding: 12px 24px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s;
        }
        
        .send-button:hover {
            transform: translateY(-2px);
        }
        
        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .voice-button {
            padding: 12px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .voice-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }

        .voice-button.recording {
            background: linear-gradient(135deg, #ff3838 0%, #ff1744 100%);
            animation: pulse 1.5s infinite;
        }

        .voice-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            animation: none;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
            }
            50% {
                box-shadow: 0 4px 25px rgba(255, 107, 107, 0.6);
            }
            100% {
                box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
            }
        }

        .voice-status {
            font-size: 12px;
            color: #6c757d;
            text-align: center;
            margin-top: 5px;
        }
        
        .typing-indicator {
            display: none;
            padding: 10px 16px;
            background: white;
            border-radius: 18px;
            margin-right: 50px;
            margin-bottom: 15px;
        }
        
        .typing-dots {
            display: flex;
            gap: 4px;
        }
        
        .typing-dot {
            width: 8px;
            height: 8px;
            background: #4facfe;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }
        
        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }
        
        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }
        
        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }
        
        .knowledge-info {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            🤖 ويليكس الذكي - المساعد الذكي
        </div>
        
        <div class="status-bar">
            <span id="status">🟢 متصل</span> |
            <span id="knowledge-count">المعرفة: 0 عنصر</span> |
            <span id="agent-status">حلقة الوكيل: غير نشطة</span> |
            <select id="languageSelect" style="margin-left: 10px; padding: 5px; border-radius: 5px; border: 1px solid #ddd;">
                <option value="ar-SA">🇸🇦 العربية</option>
                <option value="en-US">🇺🇸 English</option>
                <option value="fr-FR">🇫🇷 Français</option>
                <option value="es-ES">🇪🇸 Español</option>
                <option value="de-DE">🇩🇪 Deutsch</option>
                <option value="it-IT">🇮🇹 Italiano</option>
                <option value="pt-BR">🇧🇷 Português</option>
                <option value="ru-RU">🇷🇺 Русский</option>
                <option value="ja-JP">🇯🇵 日本語</option>
                <option value="ko-KR">🇰🇷 한국어</option>
                <option value="zh-CN">🇨🇳 中文</option>
                <option value="hi-IN">🇮🇳 हिन्दी</option>
                <option value="tr-TR">🇹🇷 Türkçe</option>
                <option value="nl-NL">🇳🇱 Nederlands</option>
                <option value="sv-SE">🇸🇪 Svenska</option>
                <option value="pl-PL">🇵🇱 Polski</option>
                <option value="he-IL">🇮🇱 עברית</option>
                <option value="fa-IR">🇮🇷 فارسی</option>
                <option value="ur-PK">🇵🇰 اردو</option>
            </select>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message bot">
                <div class="message-avatar bot-avatar">و</div>
                <div class="message-content">
                    🌍 مرحباً! أنا ويليكس، مساعدك الذكي متعدد اللغات.
                    <br><br>
                    🎤 <strong>ميزة جديدة:</strong> اضغط على زر الميكروفون وتحدث بأي لغة - سأكتشفها تلقائياً وأجيبك بنفس اللغة!
                    <br><br>
                    💬 أدعم 19 لغة مختلفة وأتعلم من كل محادثة. كيف يمكنني مساعدتك اليوم؟
                </div>
            </div>
        </div>
        
        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>
        
        <div class="chat-input">
            <input type="text" class="input-field" id="messageInput" placeholder="اكتب رسالتك هنا أو اضغط على الميكروفون..." />
            <button class="voice-button" id="voiceButton" title="اضغط للتحدث">
                🎤
            </button>
            <button class="send-button" id="sendButton">إرسال</button>
        </div>
        <div class="voice-status" id="voiceStatus"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8081';
        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const voiceButton = document.getElementById('voiceButton');
        const voiceStatus = document.getElementById('voiceStatus');
        const typingIndicator = document.getElementById('typingIndicator');
        const statusElement = document.getElementById('status');
        const knowledgeCountElement = document.getElementById('knowledge-count');
        const agentStatusElement = document.getElementById('agent-status');

        // متغيرات التحدث الصوتي
        let recognition = null;
        let isRecording = false;
        let speechSynthesis = window.speechSynthesis;
        let currentLanguage = 'ar-SA'; // اللغة الافتراضية

        // قائمة اللغات المدعومة
        const supportedLanguages = {
            'ar-SA': { name: 'العربية', voice: 'ar-SA', flag: '🇸🇦' },
            'en-US': { name: 'English', voice: 'en-US', flag: '🇺🇸' },
            'fr-FR': { name: 'Français', voice: 'fr-FR', flag: '🇫🇷' },
            'es-ES': { name: 'Español', voice: 'es-ES', flag: '🇪🇸' },
            'de-DE': { name: 'Deutsch', voice: 'de-DE', flag: '🇩🇪' },
            'it-IT': { name: 'Italiano', voice: 'it-IT', flag: '🇮🇹' },
            'pt-BR': { name: 'Português', voice: 'pt-BR', flag: '🇧🇷' },
            'ru-RU': { name: 'Русский', voice: 'ru-RU', flag: '🇷🇺' },
            'ja-JP': { name: '日本語', voice: 'ja-JP', flag: '🇯🇵' },
            'ko-KR': { name: '한국어', voice: 'ko-KR', flag: '🇰🇷' },
            'zh-CN': { name: '中文', voice: 'zh-CN', flag: '🇨🇳' },
            'hi-IN': { name: 'हिन्दी', voice: 'hi-IN', flag: '🇮🇳' },
            'tr-TR': { name: 'Türkçe', voice: 'tr-TR', flag: '🇹🇷' },
            'nl-NL': { name: 'Nederlands', voice: 'nl-NL', flag: '🇳🇱' },
            'sv-SE': { name: 'Svenska', voice: 'sv-SE', flag: '🇸🇪' },
            'pl-PL': { name: 'Polski', voice: 'pl-PL', flag: '🇵🇱' },
            'he-IL': { name: 'עברית', voice: 'he-IL', flag: '🇮🇱' },
            'fa-IR': { name: 'فارسی', voice: 'fa-IR', flag: '🇮🇷' },
            'ur-PK': { name: 'اردو', voice: 'ur-PK', flag: '🇵🇰' }
        };

        // تحديث الحالة
        async function updateStatus() {
            try {
                // حالة النظام
                const healthResponse = await fetch(`${API_BASE}/v1/health`);
                const health = await healthResponse.json();
                statusElement.textContent = health.status === 'ok' ? '🟢 متصل' : '🔴 غير متصل';

                // حالة المعرفة
                const knowledgeResponse = await fetch(`${API_BASE}/v1/knowledge/summary`);
                const knowledge = await knowledgeResponse.json();
                knowledgeCountElement.textContent = `المعرفة: ${knowledge.total_items} عنصر`;

                // حالة حلقة الوكيل
                const agentResponse = await fetch(`${API_BASE}/v1/agent-loop/status`);
                const agent = await agentResponse.json();
                agentStatusElement.textContent = `حلقة الوكيل: ${agent.is_running ? 'نشطة (' + agent.step_count + ' خطوة)' : 'غير نشطة'}`;
            } catch (error) {
                statusElement.textContent = '🔴 خطأ في الاتصال';
            }
        }

        // إضافة رسالة للمحادثة
        function addMessage(content, isUser = false, extraInfo = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : 'bot'}`;
            
            const avatar = document.createElement('div');
            avatar.className = `message-avatar ${isUser ? 'user-avatar' : 'bot-avatar'}`;
            avatar.textContent = isUser ? 'أ' : 'و';
            
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.textContent = content;
            
            if (extraInfo) {
                const infoDiv = document.createElement('div');
                infoDiv.className = 'knowledge-info';
                infoDiv.textContent = extraInfo;
                messageContent.appendChild(infoDiv);
            }
            
            if (isUser) {
                messageDiv.appendChild(messageContent);
                messageDiv.appendChild(avatar);
            } else {
                messageDiv.appendChild(avatar);
                messageDiv.appendChild(messageContent);
            }
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }



        // كشف اللغة من النص باستخدام الخادم
        async function detectLanguageFromServer(text) {
            try {
                const response = await fetch(`${API_BASE}/v1/detect-language`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: text
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    return {
                        language: mapServerLangToClient(result.detected_language),
                        name: result.language_name,
                        confidence: result.confidence
                    };
                }
            } catch (error) {
                console.log('خطأ في كشف اللغة:', error);
            }

            // كشف محلي كبديل
            return detectLanguageLocal(text);
        }

        // تحويل رموز اللغة من الخادم إلى العميل
        function mapServerLangToClient(serverLang) {
            const mapping = {
                'ar': 'ar-SA',
                'en': 'en-US',
                'fr': 'fr-FR',
                'es': 'es-ES',
                'de': 'de-DE',
                'it': 'it-IT',
                'pt': 'pt-BR',
                'ru': 'ru-RU',
                'ja': 'ja-JP',
                'ko': 'ko-KR',
                'zh': 'zh-CN',
                'hi': 'hi-IN',
                'tr': 'tr-TR',
                'fa': 'fa-IR',
                'ur': 'ur-PK',
                'he': 'he-IL'
            };
            return mapping[serverLang] || 'ar-SA';
        }

        // كشف اللغة المحلي (كبديل)
        function detectLanguageLocal(text) {
            const languagePatterns = {
                'ar-SA': /[\u0600-\u06FF]/,
                'en-US': /^[a-zA-Z\s.,!?'"]+$/,
                'fr-FR': /[àâäéèêëïîôöùûüÿç]/,
                'es-ES': /[áéíóúñü]/,
                'de-DE': /[äöüß]/,
                'it-IT': /[àèéìíîòóù]/,
                'pt-BR': /[áâãàéêíóôõú]/,
                'ru-RU': /[\u0400-\u04FF]/,
                'ja-JP': /[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/,
                'ko-KR': /[\uAC00-\uD7AF]/,
                'zh-CN': /[\u4E00-\u9FFF]/,
                'hi-IN': /[\u0900-\u097F]/,
                'tr-TR': /[çğıöşü]/,
                'he-IL': /[\u0590-\u05FF]/,
                'fa-IR': /[\u0600-\u06FF]/,
                'ur-PK': /[\u0600-\u06FF]/
            };

            for (const [lang, pattern] of Object.entries(languagePatterns)) {
                if (pattern.test(text)) {
                    return {
                        language: lang,
                        name: supportedLanguages[lang]?.name || lang,
                        confidence: 0.7
                    };
                }
            }
            return {
                language: currentLanguage,
                name: supportedLanguages[currentLanguage]?.name || currentLanguage,
                confidence: 0.5
            };
        }

        // إعداد التحدث الصوتي مع كشف اللغة التلقائي
        function initSpeechRecognition() {
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                recognition = new SpeechRecognition();
                recognition.lang = currentLanguage;
                recognition.continuous = false;
                recognition.interimResults = true; // تمكين النتائج المؤقتة لكشف اللغة

                recognition.onstart = function() {
                    isRecording = true;
                    voiceButton.classList.add('recording');
                    voiceButton.innerHTML = '🔴';
                    voiceStatus.textContent = '🎤 جاري الاستماع... تحدث بأي لغة';
                };

                recognition.onresult = async function(event) {
                    const transcript = event.results[0][0].transcript;
                    const isFinal = event.results[0].isFinal;

                    if (isFinal) {
                        // كشف اللغة من النص المنطوق
                        voiceStatus.textContent = '🔍 جاري كشف اللغة...';

                        const detectionResult = await detectLanguageFromServer(transcript);

                        // تحديث اللغة إذا كانت مختلفة
                        if (detectionResult.language !== currentLanguage) {
                            currentLanguage = detectionResult.language;
                            document.getElementById('languageSelect').value = currentLanguage;
                            updateLanguage();

                            voiceStatus.textContent = `🌍 تم كشف اللغة: ${detectionResult.name} (${Math.round(detectionResult.confidence * 100)}%)`;
                        } else {
                            voiceStatus.textContent = `✅ تم التعرف على: "${transcript}"`;
                        }

                        messageInput.value = transcript;

                        // إرسال الرسالة تلقائياً بعد ثانيتين
                        setTimeout(() => {
                            sendMessage();
                        }, 2000);
                    } else {
                        // عرض النص المؤقت أثناء التحدث
                        voiceStatus.textContent = `🎤 يستمع: "${transcript}"`;
                    }
                };

                recognition.onerror = function(event) {
                    let errorMessage = 'خطأ في التعرف على الصوت';
                    switch(event.error) {
                        case 'no-speech':
                            errorMessage = '❌ لم يتم سماع أي صوت';
                            break;
                        case 'audio-capture':
                            errorMessage = '❌ خطأ في الميكروفون';
                            break;
                        case 'not-allowed':
                            errorMessage = '❌ يرجى السماح بالوصول للميكروفون';
                            break;
                        case 'network':
                            errorMessage = '❌ خطأ في الشبكة';
                            break;
                        default:
                            errorMessage = `❌ خطأ: ${event.error}`;
                    }
                    voiceStatus.textContent = errorMessage;
                    stopRecording();
                };

                recognition.onend = function() {
                    stopRecording();
                };
            } else {
                voiceButton.disabled = true;
                voiceStatus.textContent = '❌ التحدث الصوتي غير مدعوم في هذا المتصفح';
            }
        }

        function startRecording() {
            if (recognition && !isRecording) {
                // إعادة تعيين اللغة للتعرف متعدد اللغات
                recognition.lang = 'auto'; // محاولة الكشف التلقائي

                // إذا لم يدعم المتصفح 'auto'، استخدم اللغة الحالية
                if (!recognition.lang || recognition.lang === 'auto') {
                    recognition.lang = currentLanguage;
                }

                try {
                    recognition.start();
                } catch (error) {
                    voiceStatus.textContent = '❌ خطأ في بدء التسجيل';
                    console.error('Speech recognition error:', error);
                }
            }
        }

        function stopRecording() {
            isRecording = false;
            voiceButton.classList.remove('recording');
            voiceButton.innerHTML = '🎤';
            if (recognition) {
                recognition.stop();
            }
        }

        // قراءة الرد بالصوت متعدد اللغات
        async function speakText(text) {
            if (speechSynthesis) {
                // إيقاف أي قراءة سابقة
                speechSynthesis.cancel();

                // كشف لغة النص من الخادم
                const detectionResult = await detectLanguageFromServer(text);
                const detectedLang = detectionResult.language;

                const utterance = new SpeechSynthesisUtterance(text);
                utterance.lang = detectedLang;
                utterance.rate = 0.9;
                utterance.pitch = 1;

                // البحث عن صوت مناسب للغة
                const voices = speechSynthesis.getVoices();
                let selectedVoice = null;

                // البحث عن صوت بنفس اللغة
                selectedVoice = voices.find(voice => voice.lang === detectedLang);

                // إذا لم يوجد، ابحث عن صوت بنفس رمز اللغة الأساسي
                if (!selectedVoice) {
                    const baseLang = detectedLang.split('-')[0];
                    selectedVoice = voices.find(voice => voice.lang.startsWith(baseLang));
                }

                // إذا لم يوجد، استخدم الصوت الافتراضي
                if (!selectedVoice) {
                    selectedVoice = voices.find(voice => voice.default);
                }

                if (selectedVoice) {
                    utterance.voice = selectedVoice;
                }

                // عرض معلومات اللغة المكتشفة
                if (detectionResult.confidence > 0.7) {
                    console.log(`🗣️ كشف اللغة: ${detectionResult.name} (ثقة: ${(detectionResult.confidence * 100).toFixed(0)}%)`);
                }

                speechSynthesis.speak(utterance);
            }
        }

        // تحديث دالة إرسال الرسالة لتشمل كشف اللغة التلقائي
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // كشف لغة الرسالة وتحديث الواجهة
            const detectionResult = await detectLanguageFromServer(message);
            if (detectionResult.language !== currentLanguage) {
                currentLanguage = detectionResult.language;
                document.getElementById('languageSelect').value = currentLanguage;
                updateLanguage();

                // عرض إشعار بكشف اللغة
                voiceStatus.textContent = `🌍 تم كشف اللغة: ${detectionResult.name}`;
                setTimeout(() => {
                    voiceStatus.textContent = '';
                }, 3000);
            }

            // إضافة رسالة المستخدم
            addMessage(message, true);
            messageInput.value = '';
            sendButton.disabled = true;

            // إظهار مؤشر الكتابة
            typingIndicator.style.display = 'block';
            chatMessages.scrollTop = chatMessages.scrollHeight;

            try {
                const response = await fetch(`${API_BASE}/v1/infer`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: message,
                        context: []
                    })
                });

                const result = await response.json();

                // إخفاء مؤشر الكتابة
                typingIndicator.style.display = 'none';

                if (response.ok) {
                    const extraInfo = result.ai_knowledge_used ?
                        `استخدم المعرفة المكتسبة | ${detectionResult.name} | الإصدار: ${result.model_version}` :
                        `${detectionResult.name} | الإصدار: ${result.model_version}`;

                    addMessage(result.output, false, extraInfo);

                    // قراءة الرد بالصوت
                    await speakText(result.output);

                    // تحديث الحالة بعد الرد
                    setTimeout(updateStatus, 1000);
                } else {
                    const errorMsg = 'عذراً، حدث خطأ في معالجة رسالتك. حاول مرة أخرى.';
                    addMessage(errorMsg, false);
                    await speakText(errorMsg);
                }
            } catch (error) {
                typingIndicator.style.display = 'none';
                const errorMsg = 'عذراً، لا يمكنني الاتصال بالخادم حالياً.';
                addMessage(errorMsg, false);
                await speakText(errorMsg);
            }

            sendButton.disabled = false;
        }

        // تحديث اللغة مع دعم الكشف التلقائي
        function updateLanguage() {
            currentLanguage = document.getElementById('languageSelect').value;

            // تحديث placeholder بناءً على اللغة
            const placeholders = {
                'ar-SA': '🎤 تحدث بأي لغة أو اكتب رسالتك هنا...',
                'en-US': '🎤 Speak in any language or type your message here...',
                'fr-FR': '🎤 Parlez dans n\'importe quelle langue ou tapez votre message ici...',
                'es-ES': '🎤 Habla en cualquier idioma o escribe tu mensaje aquí...',
                'de-DE': '🎤 Sprechen Sie in jeder Sprache oder geben Sie hier Ihre Nachricht ein...',
                'it-IT': '🎤 Parla in qualsiasi lingua o digita il tuo messaggio qui...',
                'pt-BR': '🎤 Fale em qualquer idioma ou digite sua mensagem aqui...',
                'ru-RU': '🎤 Говорите на любом языке или введите ваше сообщение здесь...',
                'ja-JP': '🎤 どの言語でも話すか、ここにメッセージを入力してください...',
                'ko-KR': '🎤 어떤 언어로든 말하거나 여기에 메시지를 입력하세요...',
                'zh-CN': '🎤 用任何语言说话或在此输入您的消息...',
                'hi-IN': '🎤 किसी भी भाषा में बोलें या यहाँ अपना संदेश टाइप करें...',
                'tr-TR': '🎤 Herhangi bir dilde konuşun veya mesajınızı buraya yazın...',
                'he-IL': '🎤 דבר בכל שפה או הקלד את ההודעה שלך כאן...',
                'fa-IR': '🎤 به هر زبانی صحبت کنید یا پیام خود را اینجا تایپ کنید...',
                'ur-PK': '🎤 کسی بھی زبان میں بولیں یا اپنا پیغام یہاں ٹائپ کریں...'
            };

            messageInput.placeholder = placeholders[currentLanguage] || placeholders['ar-SA'];

            // تحديث عنوان زر الصوت
            const voiceTitles = {
                'ar-SA': 'اضغط للتحدث بأي لغة - ويليكس سيكتشفها تلقائياً',
                'en-US': 'Press to speak in any language - Weilix will detect it automatically',
                'fr-FR': 'Appuyez pour parler dans n\'importe quelle langue - Weilix la détectera automatiquement',
                'es-ES': 'Presiona para hablar en cualquier idioma - Weilix lo detectará automáticamente',
                'de-DE': 'Drücken Sie, um in jeder Sprache zu sprechen - Weilix erkennt sie automatisch',
                'it-IT': 'Premi per parlare in qualsiasi lingua - Weilix la rileverà automaticamente',
                'pt-BR': 'Pressione para falar em qualquer idioma - Weilix detectará automaticamente',
                'ru-RU': 'Нажмите, чтобы говорить на любом языке - Weilix определит его автоматически',
                'ja-JP': 'どの言語でも話すために押してください - Weilixが自動的に検出します',
                'ko-KR': '어떤 언어로든 말하려면 누르세요 - Weilix가 자동으로 감지합니다',
                'zh-CN': '按下以用任何语言说话 - Weilix将自动检测',
                'hi-IN': 'किसी भी भाषा में बोलने के लिए दबाएं - Weilix इसे स्वचालित रूप से पहचान लेगा',
                'tr-TR': 'Herhangi bir dilde konuşmak için basın - Weilix otomatik olarak algılayacak',
                'he-IL': 'לחץ כדי לדבר בכל שפה - Weilix יזהה אותה אוטומטית',
                'fa-IR': 'برای صحبت به هر زبانی فشار دهید - Weilix آن را به طور خودکار تشخیص می دهد',
                'ur-PK': 'کسی بھی زبان میں بولنے کے لیے دبائیں - Weilix خودکار طور پر اس کا پتہ لگا لے گا'
            };

            voiceButton.title = voiceTitles[currentLanguage] || voiceTitles['ar-SA'];
        }

        // إضافة دعم للغات متعددة في التعرف الصوتي
        function setupMultiLanguageRecognition() {
            if (recognition) {
                // قائمة اللغات للتجربة بالترتيب
                const languagesToTry = [
                    currentLanguage,  // اللغة المختارة أولاً
                    'ar-SA', 'en-US', 'fr-FR', 'es-ES', 'de-DE',
                    'it-IT', 'pt-BR', 'ru-RU', 'ja-JP', 'ko-KR',
                    'zh-CN', 'hi-IN', 'tr-TR', 'fa-IR', 'ur-PK', 'he-IL'
                ];

                // إزالة التكرارات
                const uniqueLanguages = [...new Set(languagesToTry)];

                // تعيين اللغة الأولى للبدء
                recognition.lang = uniqueLanguages[0];
            }
        }

        // أحداث الواجهة
        sendButton.addEventListener('click', sendMessage);
        voiceButton.addEventListener('click', () => {
            if (isRecording) {
                stopRecording();
            } else {
                startRecording();
            }
        });

        document.getElementById('languageSelect').addEventListener('change', updateLanguage);

        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // تهيئة التطبيق مع دعم كشف اللغة التلقائي
        initSpeechRecognition();
        setupMultiLanguageRecognition();
        updateLanguage();
        updateStatus();
        setInterval(updateStatus, 5000);

        // رسالة ترحيب في وحدة التحكم
        console.log('🌍 ويليكس الذكي متعدد اللغات جاهز!');
        console.log('🎤 اضغط على زر الميكروفون وتحدث بأي لغة');
        console.log('🔍 سيتم كشف اللغة تلقائياً والرد بنفس اللغة');
    </script>
</body>
</html>
