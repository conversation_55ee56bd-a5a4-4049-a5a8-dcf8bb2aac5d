<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محادثة مع ويليكس الذكي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        
        .status-bar {
            background: #f8f9fa;
            padding: 10px 20px;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
            color: #6c757d;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-left: 10px;
        }
        
        .message.bot .message-content {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            margin-right: 10px;
        }
        
        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 16px;
        }
        
        .user-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .bot-avatar {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 10px;
        }
        
        .input-field {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }
        
        .input-field:focus {
            border-color: #4facfe;
        }
        
        .send-button {
            padding: 12px 24px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s;
        }
        
        .send-button:hover {
            transform: translateY(-2px);
        }
        
        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .voice-button {
            padding: 12px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .voice-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }

        .voice-button.recording {
            background: linear-gradient(135deg, #ff3838 0%, #ff1744 100%);
            animation: pulse 1.5s infinite;
        }

        .voice-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            animation: none;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
            }
            50% {
                box-shadow: 0 4px 25px rgba(255, 107, 107, 0.6);
            }
            100% {
                box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
            }
        }

        .voice-status {
            font-size: 12px;
            color: #6c757d;
            text-align: center;
            margin-top: 5px;
        }
        
        .typing-indicator {
            display: none;
            padding: 10px 16px;
            background: white;
            border-radius: 18px;
            margin-right: 50px;
            margin-bottom: 15px;
        }
        
        .typing-dots {
            display: flex;
            gap: 4px;
        }
        
        .typing-dot {
            width: 8px;
            height: 8px;
            background: #4facfe;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }
        
        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }
        
        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }
        
        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }
        
        .knowledge-info {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            🤖 ويليكس الذكي - المساعد الذكي
        </div>
        
        <div class="status-bar">
            <span id="status">🟢 متصل</span> | 
            <span id="knowledge-count">المعرفة: 0 عنصر</span> | 
            <span id="agent-status">حلقة الوكيل: غير نشطة</span>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message bot">
                <div class="message-avatar bot-avatar">و</div>
                <div class="message-content">
                    مرحباً! أنا ويليكس، مساعدك الذكي. أتعلم من كل محادثة وأطور معرفتي باستمرار. كيف يمكنني مساعدتك اليوم؟
                </div>
            </div>
        </div>
        
        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>
        
        <div class="chat-input">
            <input type="text" class="input-field" id="messageInput" placeholder="اكتب رسالتك هنا أو اضغط على الميكروفون..." />
            <button class="voice-button" id="voiceButton" title="اضغط للتحدث">
                🎤
            </button>
            <button class="send-button" id="sendButton">إرسال</button>
        </div>
        <div class="voice-status" id="voiceStatus"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8081';
        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const voiceButton = document.getElementById('voiceButton');
        const voiceStatus = document.getElementById('voiceStatus');
        const typingIndicator = document.getElementById('typingIndicator');
        const statusElement = document.getElementById('status');
        const knowledgeCountElement = document.getElementById('knowledge-count');
        const agentStatusElement = document.getElementById('agent-status');

        // متغيرات التحدث الصوتي
        let recognition = null;
        let isRecording = false;
        let speechSynthesis = window.speechSynthesis;

        // تحديث الحالة
        async function updateStatus() {
            try {
                // حالة النظام
                const healthResponse = await fetch(`${API_BASE}/v1/health`);
                const health = await healthResponse.json();
                statusElement.textContent = health.status === 'ok' ? '🟢 متصل' : '🔴 غير متصل';

                // حالة المعرفة
                const knowledgeResponse = await fetch(`${API_BASE}/v1/knowledge/summary`);
                const knowledge = await knowledgeResponse.json();
                knowledgeCountElement.textContent = `المعرفة: ${knowledge.total_items} عنصر`;

                // حالة حلقة الوكيل
                const agentResponse = await fetch(`${API_BASE}/v1/agent-loop/status`);
                const agent = await agentResponse.json();
                agentStatusElement.textContent = `حلقة الوكيل: ${agent.is_running ? 'نشطة (' + agent.step_count + ' خطوة)' : 'غير نشطة'}`;
            } catch (error) {
                statusElement.textContent = '🔴 خطأ في الاتصال';
            }
        }

        // إضافة رسالة للمحادثة
        function addMessage(content, isUser = false, extraInfo = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : 'bot'}`;
            
            const avatar = document.createElement('div');
            avatar.className = `message-avatar ${isUser ? 'user-avatar' : 'bot-avatar'}`;
            avatar.textContent = isUser ? 'أ' : 'و';
            
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.textContent = content;
            
            if (extraInfo) {
                const infoDiv = document.createElement('div');
                infoDiv.className = 'knowledge-info';
                infoDiv.textContent = extraInfo;
                messageContent.appendChild(infoDiv);
            }
            
            if (isUser) {
                messageDiv.appendChild(messageContent);
                messageDiv.appendChild(avatar);
            } else {
                messageDiv.appendChild(avatar);
                messageDiv.appendChild(messageContent);
            }
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }



        // إعداد التحدث الصوتي
        function initSpeechRecognition() {
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                recognition = new SpeechRecognition();
                recognition.lang = 'ar-SA';
                recognition.continuous = false;
                recognition.interimResults = false;

                recognition.onstart = function() {
                    isRecording = true;
                    voiceButton.classList.add('recording');
                    voiceButton.innerHTML = '🔴';
                    voiceStatus.textContent = 'جاري الاستماع... تحدث الآن';
                };

                recognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    messageInput.value = transcript;
                    voiceStatus.textContent = `تم التعرف على: "${transcript}"`;
                    setTimeout(() => {
                        sendMessage();
                    }, 1000);
                };

                recognition.onerror = function(event) {
                    voiceStatus.textContent = 'خطأ في التعرف على الصوت: ' + event.error;
                    stopRecording();
                };

                recognition.onend = function() {
                    stopRecording();
                };
            } else {
                voiceButton.disabled = true;
                voiceStatus.textContent = 'التحدث الصوتي غير مدعوم في هذا المتصفح';
            }
        }

        function startRecording() {
            if (recognition && !isRecording) {
                recognition.start();
            }
        }

        function stopRecording() {
            isRecording = false;
            voiceButton.classList.remove('recording');
            voiceButton.innerHTML = '🎤';
            if (recognition) {
                recognition.stop();
            }
        }

        // قراءة الرد بالصوت
        function speakText(text) {
            if (speechSynthesis) {
                // إيقاف أي قراءة سابقة
                speechSynthesis.cancel();

                const utterance = new SpeechSynthesisUtterance(text);
                utterance.lang = 'ar-SA';
                utterance.rate = 0.9;
                utterance.pitch = 1;

                // البحث عن صوت عربي
                const voices = speechSynthesis.getVoices();
                const arabicVoice = voices.find(voice => voice.lang.includes('ar'));
                if (arabicVoice) {
                    utterance.voice = arabicVoice;
                }

                speechSynthesis.speak(utterance);
            }
        }

        // تحديث دالة إرسال الرسالة لتشمل القراءة الصوتية
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // إضافة رسالة المستخدم
            addMessage(message, true);
            messageInput.value = '';
            sendButton.disabled = true;
            voiceStatus.textContent = '';

            // إظهار مؤشر الكتابة
            typingIndicator.style.display = 'block';
            chatMessages.scrollTop = chatMessages.scrollHeight;

            try {
                const response = await fetch(`${API_BASE}/v1/infer`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: message,
                        context: []
                    })
                });

                const result = await response.json();

                // إخفاء مؤشر الكتابة
                typingIndicator.style.display = 'none';

                if (response.ok) {
                    const extraInfo = result.ai_knowledge_used ?
                        `استخدم المعرفة المكتسبة | الإصدار: ${result.model_version}` :
                        `الإصدار: ${result.model_version}`;

                    addMessage(result.output, false, extraInfo);

                    // قراءة الرد بالصوت
                    speakText(result.output);

                    // تحديث الحالة بعد الرد
                    setTimeout(updateStatus, 1000);
                } else {
                    const errorMsg = 'عذراً، حدث خطأ في معالجة رسالتك. حاول مرة أخرى.';
                    addMessage(errorMsg, false);
                    speakText(errorMsg);
                }
            } catch (error) {
                typingIndicator.style.display = 'none';
                const errorMsg = 'عذراً، لا يمكنني الاتصال بالخادم حالياً.';
                addMessage(errorMsg, false);
                speakText(errorMsg);
            }

            sendButton.disabled = false;
        }

        // أحداث الواجهة
        sendButton.addEventListener('click', sendMessage);
        voiceButton.addEventListener('click', () => {
            if (isRecording) {
                stopRecording();
            } else {
                startRecording();
            }
        });

        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // تهيئة التطبيق
        initSpeechRecognition();
        updateStatus();
        setInterval(updateStatus, 5000);
    </script>
</body>
</html>
