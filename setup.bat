@echo off
echo ========================================
echo Setting up Weilix MVP v2 Environment
echo ========================================

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.11 or newer
    pause
    exit /b 1
)

echo Python found successfully

REM Create virtual environment if it doesn't exist
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo ERROR: Failed to create virtual environment
        pause
        exit /b 1
    )
    echo Virtual environment created successfully
) else (
    echo Virtual environment already exists
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ERROR: Failed to activate virtual environment
    pause
    exit /b 1
)

echo Virtual environment activated successfully

REM Upgrade pip
echo Upgrading pip...
python -m pip install --upgrade pip

REM Navigate to services/core
cd services\core

REM Install requirements
echo Installing requirements from requirements.txt...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ERROR: Failed to install requirements
    pause
    exit /b 1
)

echo All requirements installed successfully

REM Go back to root directory
cd ..\..

echo ========================================
echo Environment setup completed successfully!
echo ========================================
echo.
echo To run the project:
echo 1. Activate environment: venv\Scripts\activate
echo 2. Run server: run_weilix.bat
echo.
echo Or use: docker-compose up
echo ========================================
pause
