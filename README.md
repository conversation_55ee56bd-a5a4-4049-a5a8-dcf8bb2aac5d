
# Weilix – MVP (v2)

نواة خدمة **Weilix Core** (Python + FastAPI) مع Policy SDK بسيط.
شغّل `/v1/health`, `/v1/infer`, `/v1/evolve` كبداية.

## 🚀 تشغيل سريع

### الطريقة الأسهل (Windows)
```bash
# تشغيل سريع - إعداد وتشغيل تلقائي
quick_start.bat
```

### إعداد يدوي (Windows)
```bash
# 1. إعداد البيئة
setup_environment.bat

# 2. تشغيل الخادم
run_weilix.bat
```

### إعداد يدوي (PowerShell)
```powershell
# 1. إعداد البيئة
.\setup_environment.ps1

# 2. تفعيل البيئة وتشغيل الخادم
.\venv\Scripts\Activate.ps1
cd services\core
uvicorn main:app --reload --port 8080
```

### باستخدام Docker
```bash
# تشغيل مع قاعدة البيانات
docker-compose up
```

## 🧪 اختبار
```bash
# اختبار صحة الخدمة
curl http://127.0.0.1:8080/v1/health

# اختبار الذكاء الاصطناعي
curl -s http://127.0.0.1:8080/v1/infer -H "Content-Type: application/json" -d "{\"prompt\":\"مرحبا يا ويليكس\"}"

# فتح واجهة الويب
start client.html
```

## 📁 هيكل المشروع
```
weilix_mvp_v2/
├── services/core/          # خدمة Weilix الأساسية
│   ├── main.py            # تطبيق FastAPI الرئيسي
│   ├── requirements.txt   # متطلبات Python
│   └── Dockerfile         # إعداد Docker
├── libs/policy_sdk/       # مكتبة السياسات
│   └── policy.py          # PolicyRouter
├── configs/               # ملفات الإعداد
│   └── constitution.yaml  # دستور Weilix
├── client.html           # واجهة ويب بسيطة
├── docker-compose.yml    # إعداد Docker Compose
└── setup_environment.bat # إعداد البيئة
```

## 🔧 متطلبات النظام
- Python 3.11 أو أحدث
- MySQL 8.0 (للإنتاج) أو SQLite (للتطوير)
- Docker & Docker Compose (اختياري)

## 🌐 نقاط النهاية (API Endpoints)
- `GET /v1/health` - فحص صحة الخدمة
- `GET /v1/db-ping` - فحص اتصال قاعدة البيانات
- `POST /v1/infer` - معالجة النصوص بالذكاء الاصطناعي
- `POST /v1/evolve` - جدولة التدريب
- `GET/POST /v1/agents` - إدارة الوكلاء
- `GET/POST /v1/tasks` - إدارة المهام

## 🛠️ استكشاف الأخطاء
إذا واجهت مشكلة "Import could not be resolved":
1. تأكد من تفعيل البيئة الافتراضية
2. شغّل `setup_environment.bat`
3. أعد تشغيل VS Code بعد إعداد البيئة
