<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار كشف اللغة - ويليكس</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .test-area {
            margin-bottom: 30px;
        }
        
        textarea {
            width: 100%;
            height: 100px;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            resize: vertical;
        }
        
        button {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        
        button:hover {
            transform: translateY(-2px);
        }
        
        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .language-info {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
        
        .confidence-bar {
            background: #e9ecef;
            height: 10px;
            border-radius: 5px;
            overflow: hidden;
            flex: 1;
        }
        
        .confidence-fill {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .example:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        
        .example-flag {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .example-text {
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌍 اختبار كشف اللغة - ويليكس الذكي</h1>
        
        <div class="test-area">
            <textarea id="textInput" placeholder="اكتب أي نص بأي لغة هنا..."></textarea>
            <button onclick="detectLanguage()">🔍 كشف اللغة</button>
            <button onclick="clearText()">🗑️ مسح</button>
        </div>
        
        <div id="result" class="result" style="display: none;">
            <h3>نتيجة كشف اللغة:</h3>
            <div class="language-info">
                <span id="languageFlag" style="font-size: 32px;"></span>
                <div style="flex: 1;">
                    <div><strong id="languageName"></strong></div>
                    <div style="font-size: 14px; color: #666;" id="languageCode"></div>
                </div>
                <div style="width: 100px;">
                    <div class="confidence-bar">
                        <div class="confidence-fill" id="confidenceBar"></div>
                    </div>
                    <div style="text-align: center; font-size: 12px; margin-top: 5px;" id="confidenceText"></div>
                </div>
            </div>
        </div>
        
        <h3>أمثلة للاختبار:</h3>
        <div class="examples">
            <div class="example" onclick="setExample('مرحبا، كيف حالك اليوم؟')">
                <div class="example-flag">🇸🇦</div>
                <div><strong>العربية</strong></div>
                <div class="example-text">مرحبا، كيف حالك اليوم؟</div>
            </div>
            
            <div class="example" onclick="setExample('Hello, how are you today?')">
                <div class="example-flag">🇺🇸</div>
                <div><strong>English</strong></div>
                <div class="example-text">Hello, how are you today?</div>
            </div>
            
            <div class="example" onclick="setExample('Bonjour, comment allez-vous?')">
                <div class="example-flag">🇫🇷</div>
                <div><strong>Français</strong></div>
                <div class="example-text">Bonjour, comment allez-vous?</div>
            </div>
            
            <div class="example" onclick="setExample('Hola, ¿cómo estás hoy?')">
                <div class="example-flag">🇪🇸</div>
                <div><strong>Español</strong></div>
                <div class="example-text">Hola, ¿cómo estás hoy?</div>
            </div>
            
            <div class="example" onclick="setExample('Hallo, wie geht es dir heute?')">
                <div class="example-flag">🇩🇪</div>
                <div><strong>Deutsch</strong></div>
                <div class="example-text">Hallo, wie geht es dir heute?</div>
            </div>
            
            <div class="example" onclick="setExample('こんにちは、今日はいかがですか？')">
                <div class="example-flag">🇯🇵</div>
                <div><strong>日本語</strong></div>
                <div class="example-text">こんにちは、今日はいかがですか？</div>
            </div>
            
            <div class="example" onclick="setExample('你好，你今天怎么样？')">
                <div class="example-flag">🇨🇳</div>
                <div><strong>中文</strong></div>
                <div class="example-text">你好，你今天怎么样？</div>
            </div>
            
            <div class="example" onclick="setExample('Привет, как дела сегодня?')">
                <div class="example-flag">🇷🇺</div>
                <div><strong>Русский</strong></div>
                <div class="example-text">Привет, как дела сегодня?</div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8081';
        
        const languageFlags = {
            'ar': '🇸🇦',
            'en': '🇺🇸',
            'fr': '🇫🇷',
            'es': '🇪🇸',
            'de': '🇩🇪',
            'it': '🇮🇹',
            'pt': '🇧🇷',
            'ru': '🇷🇺',
            'ja': '🇯🇵',
            'ko': '🇰🇷',
            'zh': '🇨🇳',
            'hi': '🇮🇳',
            'tr': '🇹🇷',
            'fa': '🇮🇷',
            'ur': '🇵🇰',
            'he': '🇮🇱'
        };

        async function detectLanguage() {
            const text = document.getElementById('textInput').value.trim();
            if (!text) {
                alert('يرجى إدخال نص للاختبار');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/v1/detect-language`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: text
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    displayResult(result);
                } else {
                    alert('خطأ في كشف اللغة');
                }
            } catch (error) {
                alert('خطأ في الاتصال بالخادم');
                console.error(error);
            }
        }

        function displayResult(result) {
            const resultDiv = document.getElementById('result');
            const languageFlag = document.getElementById('languageFlag');
            const languageName = document.getElementById('languageName');
            const languageCode = document.getElementById('languageCode');
            const confidenceBar = document.getElementById('confidenceBar');
            const confidenceText = document.getElementById('confidenceText');

            languageFlag.textContent = languageFlags[result.detected_language] || '🌍';
            languageName.textContent = result.language_name;
            languageCode.textContent = `رمز اللغة: ${result.detected_language}`;
            
            const confidencePercent = Math.round(result.confidence * 100);
            confidenceBar.style.width = `${confidencePercent}%`;
            confidenceText.textContent = `${confidencePercent}%`;

            resultDiv.style.display = 'block';
        }

        function setExample(text) {
            document.getElementById('textInput').value = text;
            detectLanguage();
        }

        function clearText() {
            document.getElementById('textInput').value = '';
            document.getElementById('result').style.display = 'none';
        }
    </script>
</body>
</html>
