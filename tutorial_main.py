# main.py - الملف الرئيسي للتطبيق
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
from typing import List, Optional
import uvicorn

# إنشاء تطبيق FastAPI
app = FastAPI(
    title="My AI App",
    description="تطبيق ذكي بسيط",
    version="1.0.0"
)

# إعداد CORS للسماح بالوصول من المتصفح
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # في الإنتاج، حدد النطاقات المسموحة
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# نماذج البيانات (Pydantic Models)
class ChatRequest(BaseModel):
    message: str
    context: Optional[List[str]] = []

class ChatResponse(BaseModel):
    response: str
    timestamp: str
    model_version: str

# قاعدة بيانات بسيطة في الذاكرة
chat_history = []

# خدمة الذكاء الاصطناعي البسيطة
class SimpleAI:
    def __init__(self):
        self.responses = {
            "مرحبا": "مرحباً بك! كيف يمكنني مساعدتك؟",
            "كيف حالك": "أنا بخير، شكراً لسؤالك!",
            "ما اسمك": "اسمي مساعد ذكي، يسعدني خدمتك",
            "وداعا": "وداعاً! أتمنى لك يوماً سعيداً"
        }
    
    def generate_response(self, message: str) -> str:
        message_lower = message.lower().strip()
        
        # البحث عن رد مناسب
        for key, response in self.responses.items():
            if key in message_lower:
                return response
        
        # رد افتراضي
        return f"شكراً لرسالتك: '{message}'. كيف يمكنني مساعدتك أكثر؟"

# إنشاء مثيل من خدمة الذكاء الاصطناعي
ai_service = SimpleAI()

# نقاط النهاية (API Endpoints)

@app.get("/")
async def read_root():
    """الصفحة الرئيسية"""
    return {"message": "مرحباً بك في تطبيقي الذكي!"}

@app.get("/health")
async def health_check():
    """فحص صحة التطبيق"""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "message": "التطبيق يعمل بشكل طبيعي"
    }

@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """نقطة نهاية المحادثة"""
    try:
        # توليد الرد
        ai_response = ai_service.generate_response(request.message)
        
        # حفظ المحادثة
        chat_entry = {
            "user_message": request.message,
            "ai_response": ai_response,
            "timestamp": "2024-01-01T00:00:00Z"  # في التطبيق الحقيقي، استخدم datetime.now()
        }
        chat_history.append(chat_entry)
        
        return ChatResponse(
            response=ai_response,
            timestamp=chat_entry["timestamp"],
            model_version="simple-ai-v1.0"
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في المعالجة: {str(e)}")

@app.get("/history")
async def get_chat_history():
    """الحصول على تاريخ المحادثات"""
    return {"history": chat_history}

@app.delete("/history")
async def clear_chat_history():
    """مسح تاريخ المحادثات"""
    global chat_history
    chat_history = []
    return {"message": "تم مسح تاريخ المحادثات"}

# تشغيل التطبيق
if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True  # إعادة التحميل التلقائي عند تغيير الكود
    )
