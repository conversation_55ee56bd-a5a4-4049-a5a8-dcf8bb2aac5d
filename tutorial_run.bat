@echo off
echo ========================================
echo تشغيل التطبيق الذكي
echo ========================================

REM تفعيل البيئة الافتراضية
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
) else (
    echo خطأ: البيئة الافتراضية غير موجودة
    echo شغّل setup.bat أولاً
    pause
    exit /b 1
)

echo بدء تشغيل الخادم...
echo الخادم سيعمل على: http://localhost:8000
echo اضغط Ctrl+C لإيقاف الخادم
echo ========================================

python main.py
