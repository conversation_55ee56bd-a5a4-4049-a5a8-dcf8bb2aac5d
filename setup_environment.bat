@echo off
echo ========================================
echo إعداد بيئة Weilix MVP v2
echo ========================================

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.11 أو أحدث
    pause
    exit /b 1
)

echo ✓ تم العثور على Python

REM إنشاء البيئة الافتراضية إذا لم تكن موجودة
if not exist "venv" (
    echo إنشاء البيئة الافتراضية...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo خطأ في إنشاء البيئة الافتراضية
        pause
        exit /b 1
    )
    echo ✓ تم إنشاء البيئة الافتراضية
) else (
    echo ✓ البيئة الافتراضية موجودة
)

REM تفعيل البيئة الافتراضية
echo تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo خطأ في تفعيل البيئة الافتراضية
    pause
    exit /b 1
)

echo ✓ تم تفعيل البيئة الافتراضية

REM تحديث pip
echo تحديث pip...
python -m pip install --upgrade pip

REM الانتقال إلى مجلد services/core
cd services\core

REM تثبيت المتطلبات
echo تثبيت المتطلبات من requirements.txt...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo خطأ في تثبيت المتطلبات
    pause
    exit /b 1
)

echo ✓ تم تثبيت جميع المتطلبات بنجاح

REM العودة إلى المجلد الرئيسي
cd ..\..

echo ========================================
echo تم إعداد البيئة بنجاح!
echo ========================================
echo 
echo لتشغيل المشروع:
echo 1. تفعيل البيئة: venv\Scripts\activate
echo 2. تشغيل الخادم: run_weilix.bat
echo 
echo أو استخدم: docker-compose up
echo ========================================
pause
