import os
import sys
from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime
import time
import threading
import random

import uvicorn
import yaml
from fastapi import FastAPI, HTTPException, Depends, WebSocket, WebSocketDisconnect, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import json
import asyncio

# ===== ربط Policy SDK =====
sys.path.append(str((Path(__file__).resolve().parents[2] / "libs")))
from policy_sdk.policy import PolicyRouter  # type: ignore

# ===== قاعدة البيانات (SQLAlchemy) =====
from sqlalchemy import (
    create_engine, text, String, Enum, Text, JSON,
    TIMESTAMP, ForeignKey, Integer, BigInteger, Boolean, Float
)
from sqlalchemy.orm import (
    sessionmaker, DeclarativeBase, Mapped, mapped_column, Session
)

# بيانات الاتصال (عدّل كلمة السر إذا غيّرتها)
DB_URL = os.environ.get(
    "DB_URL",
    "mysql+pymysql://weilix_user:StrongPass!123@127.0.0.1:3306/weilix_db"
)
engine = create_engine(DB_URL, pool_pre_ping=True, future=True)
SessionLocal = sessionmaker(bind=engine, autoflush=False, autocommit=False, future=True)

class Base(DeclarativeBase):
    pass

# ========== جداول ORM ==========
class Agent(Base):
    __tablename__ = "agents"
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    code: Mapped[str] = mapped_column(String(64), unique=True, nullable=False)  # anat-1 / inana-3
    kind: Mapped[str] = mapped_column(Enum("ANAT", "INANA", name="agent_kind"), nullable=False)
    status: Mapped[str] = mapped_column(Enum("ACTIVE", "PAUSED", "RETIRED", name="agent_status"), default="ACTIVE")
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow)

class Task(Base):
    __tablename__ = "tasks"
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    agent_id: Mapped[int] = mapped_column(BigInteger, ForeignKey("agents.id"), nullable=False)
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    details: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    priority: Mapped[int] = mapped_column(Integer, default=2)  # 1 عالي / 2 عادي / 3 منخفض
    risk_score: Mapped[int] = mapped_column(Integer, default=0)  # 0-100 درجة المخاطرة
    state: Mapped[str] = mapped_column(Enum("QUEUED", "RUNNING", "DONE", "FAILED", name="task_state"), default="QUEUED")
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow)

class Message(Base):
    __tablename__ = "messages"
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    agent_id: Mapped[Optional[int]] = mapped_column(BigInteger, ForeignKey("agents.id"), nullable=True)
    role: Mapped[str] = mapped_column(Enum("USER", "WEILIX", "SYSTEM", name="msg_role"), nullable=False)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    meta: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow)

class PolicyDoc(Base):
    __tablename__ = "policies"
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    policy_id: Mapped[str] = mapped_column(String(128), nullable=False)
    version: Mapped[str] = mapped_column(String(32), nullable=False)
    body: Mapped[str] = mapped_column(Text, nullable=False)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow)

class AuditLog(Base):
    __tablename__ = "audit_logs"
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    event: Mapped[str] = mapped_column(String(64), nullable=False)
    payload: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    prev_hash: Mapped[Optional[str]] = mapped_column(String(64), nullable=True)
    hash: Mapped[str] = mapped_column(String(64), nullable=False)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow)

class TaskLog(Base):
    __tablename__ = "task_logs"
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    task_id: Mapped[int] = mapped_column(BigInteger, ForeignKey("tasks.id"), nullable=False)
    from_state: Mapped[Optional[str]] = mapped_column(String(16), nullable=True)
    to_state: Mapped[str] = mapped_column(String(16), nullable=False)
    note: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow)

# ===== جداول جديدة للذكاء الاصطناعي =====
class KnowledgeItem(Base):
    __tablename__ = "knowledge_items"
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    key: Mapped[str] = mapped_column(String(128), unique=True, nullable=False)
    value: Mapped[str] = mapped_column(Text, nullable=False)
    confidence: Mapped[float] = mapped_column(Float, default=0.5)  # 0.0 - 1.0
    source: Mapped[Optional[str]] = mapped_column(String(64), nullable=True)  # "experience", "training", etc.
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow)

class ModelState(Base):
    __tablename__ = "model_states"
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    key: Mapped[str] = mapped_column(String(64), unique=True, nullable=False)
    value: Mapped[str] = mapped_column(Text, nullable=False)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow)

# إنشاء الجداول إن لم تكن موجودة (مع معالجة أخطاء الاتصال)
try:
    Base.metadata.create_all(bind=engine)
    print("✅ تم الاتصال بقاعدة البيانات بنجاح")
except Exception as e:
    print(f"⚠️ تحذير: لا يمكن الاتصال بقاعدة البيانات: {e}")
    print("🔄 سيعمل التطبيق بدون قاعدة البيانات (وضع الاختبار)")

# تبعية جلسة DB
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# ===== إعداد التطبيق/السياسات =====
APP_VERSION = "weilix-core-v2-ai"
CONSTITUTION_PATH = os.environ.get(
    "CONSTITUTION",
    str(Path(__file__).resolve().parents[2] / "configs" / "constitution.yaml")
)
with open(CONSTITUTION_PATH, "r", encoding="utf-8") as f:
    POLICY_DOC = yaml.safe_load(f)
policy_router = PolicyRouter(POLICY_DOC)

LAST_HASH = ""

# ===== CoreAI البسيطة =====
class CoreAI:
    def __init__(self):
        self.knowledge: Dict[str, Any] = {}
        self.experience_count = 0
        
    def think(self, prompt: str, context: List[str] = None) -> str:
        """تفكير بسيط بناءً على المعرفة المخزنة"""
        if context is None:
            context = []
            
        # البحث في المعرفة المخزنة
        relevant_knowledge = []
        for key, value in self.knowledge.items():
            if any(word in key.lower() for word in prompt.lower().split()):
                relevant_knowledge.append(f"{key}: {value}")
        
        # توليد رد بسيط
        if "مرحبا" in prompt.lower() or "hello" in prompt.lower():
            return "مرحباً! أنا ويليكس، مساعدك الذكي. كيف يمكنني مساعدتك؟"
        elif "اسمك" in prompt.lower() or "name" in prompt.lower():
            return "اسمي ويليكس، وأنا نظام ذكاء اصطناعي مصمم لمساعدتك."
        elif relevant_knowledge:
            return f"بناءً على معرفتي: {'; '.join(relevant_knowledge[:2])}"
        else:
            return f"【Weilix AI】 فهمت طلبك: {prompt}. أعمل على تطوير فهمي لهذا الموضوع."
    
    def learn(self, key: str, value: str, confidence: float = 0.7):
        """تعلم معلومة جديدة"""
        self.knowledge[key] = {"value": value, "confidence": confidence, "learned_at": datetime.utcnow()}
        self.experience_count += 1
        
    def get_knowledge_summary(self) -> Dict[str, Any]:
        """ملخص المعرفة المكتسبة"""
        return {
            "total_items": len(self.knowledge),
            "experience_count": self.experience_count,
            "recent_items": list(self.knowledge.keys())[-5:] if self.knowledge else []
        }

# إنشاء مثيل CoreAI العالمي
core_ai = CoreAI()

# ===== Agent Loop =====
class AgentLoop:
    def __init__(self):
        self.is_running = False
        self.loop_thread = None
        self.step_count = 0
        
    def start(self):
        """بدء حلقة الوكيل"""
        if self.is_running:
            return {"status": "already_running"}
            
        self.is_running = True
        self.loop_thread = threading.Thread(target=self._run_loop, daemon=True)
        self.loop_thread.start()
        return {"status": "started", "step_count": self.step_count}
    
    def stop(self):
        """إيقاف حلقة الوكيل"""
        self.is_running = False
        return {"status": "stopped", "step_count": self.step_count}
    
    def status(self):
        """حالة حلقة الوكيل"""
        return {
            "is_running": self.is_running,
            "step_count": self.step_count,
            "knowledge_summary": core_ai.get_knowledge_summary()
        }
    
    def step(self):
        """خطوة واحدة من حلقة الوكيل"""
        return self._execute_step()
    
    def _run_loop(self):
        """تشغيل الحلقة الرئيسية"""
        while self.is_running:
            try:
                self._execute_step()
                time.sleep(5)  # انتظار 5 ثوانٍ بين الخطوات
            except Exception as e:
                print(f"خطأ في حلقة الوكيل: {e}")
                time.sleep(10)
    
    def _execute_step(self):
        """تنفيذ خطوة واحدة: Plan → Act → Reflect → Learn"""
        self.step_count += 1
        
        try:
            with SessionLocal() as db:
                # Plan: البحث عن مهمة للتنفيذ
                task = db.query(Task).filter(Task.state == "QUEUED").order_by(Task.priority.asc(), Task.created_at.asc()).first()
                
                if not task:
                    return {"step": self.step_count, "action": "no_tasks", "status": "waiting"}
                
                # Act: تنفيذ المهمة
                task.state = "RUNNING"
                db.add(task)
                
                # سجل بداية التنفيذ
                log = TaskLog(task_id=task.id, from_state="QUEUED", to_state="RUNNING", note="بدء التنفيذ بواسطة AgentLoop")
                db.add(log)
                db.commit()
                
                # محاكاة تنفيذ المهمة
                result = self._simulate_task_execution(task)
                
                # Reflect: تقييم النتيجة
                reflection = self._reflect_on_result(task, result)
                
                # Learn: تعلم من التجربة
                self._learn_from_experience(task, result, reflection)
                
                # تحديث حالة المهمة
                final_state = "DONE" if result["success"] else "FAILED"
                task.state = final_state
                db.add(task)
                
                # سجل انتهاء التنفيذ
                final_log = TaskLog(
                    task_id=task.id, 
                    from_state="RUNNING", 
                    to_state=final_state, 
                    note=f"انتهى التنفيذ: {result['message']}"
                )
                db.add(final_log)
                db.commit()
                
                return {
                    "step": self.step_count,
                    "action": "processed_task",
                    "task_id": task.id,
                    "task_title": task.title,
                    "result": result,
                    "reflection": reflection,
                    "final_state": final_state
                }
                
        except Exception as e:
            return {"step": self.step_count, "action": "error", "error": str(e)}
    
    def _simulate_task_execution(self, task: Task) -> Dict[str, Any]:
        """محاكاة تنفيذ المهمة"""
        # محاكاة بسيطة: نجاح عشوائي مع تأثير درجة المخاطرة
        success_probability = max(0.3, 1.0 - (task.risk_score / 200.0))  # كلما زادت المخاطرة قل احتمال النجاح
        success = random.random() < success_probability
        
        execution_time = random.uniform(1, 3)  # وقت تنفيذ عشوائي
        time.sleep(execution_time)
        
        if success:
            message = f"تم تنفيذ المهمة '{task.title}' بنجاح"
        else:
            message = f"فشل في تنفيذ المهمة '{task.title}' - مخاطرة عالية أو تعقيد غير متوقع"
        
        return {
            "success": success,
            "execution_time": execution_time,
            "message": message,
            "risk_score": task.risk_score
        }
    
    def _reflect_on_result(self, task: Task, result: Dict[str, Any]) -> Dict[str, Any]:
        """تأمل وتقييم النتيجة"""
        reflection = {
            "task_complexity": "عالي" if task.risk_score > 50 else "متوسط" if task.risk_score > 20 else "منخفض",
            "performance": "ممتاز" if result["success"] and result["execution_time"] < 2 else "جيد" if result["success"] else "يحتاج تحسين",
            "lessons": []
        }
        
        if result["success"]:
            reflection["lessons"].append("النهج المستخدم فعال لهذا النوع من المهام")
        else:
            reflection["lessons"].append("يجب تطوير استراتيجيات أفضل للمهام عالية المخاطرة")
            
        if result["execution_time"] > 2.5:
            reflection["lessons"].append("يمكن تحسين كفاءة التنفيذ")
            
        return reflection
    
    def _learn_from_experience(self, task: Task, result: Dict[str, Any], reflection: Dict[str, Any]):
        """تعلم من التجربة"""
        # تعلم نمط المهمة
        task_pattern = f"مهمة_{task.priority}_{task.risk_score//20}"
        success_rate = "نجح" if result["success"] else "فشل"
        
        core_ai.learn(
            key=f"{task_pattern}_نتيجة",
            value=f"{success_rate} في {result['execution_time']:.1f} ثانية",
            confidence=0.8 if result["success"] else 0.6
        )
        
        # تعلم من الدروس المستفادة
        for lesson in reflection["lessons"]:
            core_ai.learn(
                key=f"درس_{self.step_count}",
                value=lesson,
                confidence=0.7
            )

# إنشاء مثيل AgentLoop العالمي
agent_loop = AgentLoop()

# ===== WebSocket Management =====
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        try:
            await websocket.send_text(message)
        except:
            self.disconnect(websocket)

    async def broadcast(self, message: str):
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                disconnected.append(connection)

        # إزالة الاتصالات المنقطعة
        for conn in disconnected:
            self.disconnect(conn)

manager = ConnectionManager()

# ===== Risk Scoring (Tasks) =====
HIGH_KEYWORDS = {"اختراق", "تخطي", "تعطيل", "تجسس", "root", "priv-esc", "privilege", "phishing", "ddos", "هجوم", "malware"}
MED_KEYWORDS  = {"تحذير", "ثغرة", "leak", "leaks", "bruteforce", "anomaly", "مشبوه", "suspicious"}

def score_task_risk(title: str, details: dict | None) -> tuple[int, int]:
    """
    يعيد (risk_score, suggested_priority)
    risk_score: 0..100
    suggested_priority: 1 (عالي) / 2 (عادي) / 3 (منخفض)
    """
    text = (title or "")
    if details:
        try:
            import json
            text += " " + json.dumps(details, ensure_ascii=False)
        except Exception:
            pass
    text_norm = text.lower()

    score = 0
    for kw in HIGH_KEYWORDS:
        if kw in text_norm:
            score += 25
    for kw in MED_KEYWORDS:
        if kw in text_norm:
            score += 10

    score = max(0, min(100, score))
    if score >= 40:
        pr = 1
    elif score >= 15:
        pr = 2
    else:
        pr = 3
    return score, pr

# ===== نماذج API =====
class InferReq(BaseModel):
    prompt: str
    context: List[str] = Field(default_factory=list)  # بدلاً من [] لتجنب mutable default

class InferRes(BaseModel):
    output: str
    model_version: str
    audit_hash: str
    ai_knowledge_used: bool = False
    # إخفاء تحذير Pydantic بشأن model_
    model_config = {"protected_namespaces": ()}

class AgentCreate(BaseModel):
    code: str = Field(..., examples=["anat-1"])
    kind: str = Field(..., pattern="^(ANAT|INANA)$")

class AgentOut(BaseModel):
    id: int
    code: str
    kind: str
    status: str
    created_at: datetime

class TaskCreate(BaseModel):
    agent_id: int
    title: str
    details: Optional[dict] = None
    priority: int = 2  # سيتم تحديثه تلقائياً بناءً على تقدير المخاطرة

class TaskOut(BaseModel):
    id: int
    agent_id: int
    title: str
    details: Optional[dict]
    priority: int
    risk_score: int  # 0-100 درجة المخاطرة
    state: str
    created_at: datetime

class TaskStateUpdate(BaseModel):
    to_state: str = Field(..., pattern="^(QUEUED|RUNNING|DONE|FAILED)$")
    note: Optional[str] = None

class AgentHeartbeat(BaseModel):
    meta: Optional[dict] = None
    set_status: Optional[str] = Field(None, pattern="^(ACTIVE|PAUSED|RETIRED)$")

class RiskAssessmentRequest(BaseModel):
    title: str
    details: Optional[dict] = None

class RiskAssessmentResponse(BaseModel):
    risk_score: int  # 0-100
    suggested_priority: int  # 1, 2, 3
    risk_level: str  # "عالي", "متوسط", "منخفض"
    detected_keywords: List[str]

class KnowledgeItemCreate(BaseModel):
    key: str
    value: str
    confidence: float = Field(default=0.5, ge=0.0, le=1.0)
    source: Optional[str] = None

class KnowledgeItemOut(BaseModel):
    id: int
    key: str
    value: str
    confidence: float
    source: Optional[str]
    created_at: datetime
    updated_at: datetime

# ===== تطبيق FastAPI =====
app = FastAPI(title="Weilix AI API", version=APP_VERSION)

# ملاحظة: لا يجوز set allow_credentials=True مع "*" لذا جعلناها False
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://127.0.0.1:8080",
        "http://localhost:8080",
        "https://127.0.0.1:8443",
        "https://localhost:8443",
        "*",
    ],
    allow_credentials=False,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ===== مسارات النظام =====
@app.get("/v1/health")
def health():
    return {"status": "ok", "version": APP_VERSION, "ai_status": "active"}

@app.get("/v1/db-ping")
def db_ping():
    try:
        with engine.connect() as conn:
            r = conn.execute(text("SELECT 1")).scalar()
            return {"db": "ok" if r == 1 else "fail"}
    except Exception as e:
        return {"db": "error", "detail": str(e)}

# ===== مسارات الذكاء الاصطناعي =====
@app.post("/v1/infer", response_model=InferRes)
async def infer(req: InferReq):
    global LAST_HASH
    ok, msg = policy_router.precheck(req.prompt)
    if not ok:
        raise HTTPException(status_code=400, detail=msg)

    # استخدام CoreAI للتفكير
    out = core_ai.think(req.prompt, req.context)
    out = policy_router.postcheck(out)

    # تعلم من التفاعل
    core_ai.learn(f"سؤال_{len(core_ai.knowledge)}", req.prompt, confidence=0.6)

    record = {
        "ts": datetime.utcnow().isoformat() + "Z",
        "endpoint": "/v1/infer",
        "input_len": len(req.prompt),
        "output_len": len(out),
        "version": APP_VERSION,
        "ai_enhanced": True
    }
    LAST_HASH = policy_router.hash_chain(LAST_HASH, record)

    # بث الرد لجميع المتصلين عبر WebSocket
    response_data = {
        "type": "inference_response",
        "prompt": req.prompt,
        "output": out,
        "model_version": APP_VERSION,
        "audit_hash": LAST_HASH,
        "timestamp": record["ts"],
        "ai_knowledge_items": len(core_ai.knowledge)
    }
    await manager.broadcast(json.dumps(response_data, ensure_ascii=False))

    return InferRes(
        output=out,
        model_version=APP_VERSION,
        audit_hash=LAST_HASH,
        ai_knowledge_used=len(core_ai.knowledge) > 0
    )

@app.post("/v1/evolve")
def evolve():
    global LAST_HASH
    task = {
        "ts": datetime.utcnow().isoformat() + "Z",
        "endpoint": "/v1/evolve",
        "action": "schedule_training",
        "knowledge_items": len(core_ai.knowledge)
    }
    LAST_HASH = policy_router.hash_chain(LAST_HASH, task)
    return {"status": "scheduled", "audit_hash": LAST_HASH, "version": APP_VERSION}

# ===== مسارات AgentLoop =====
@app.post("/v1/agent-loop/start")
def start_agent_loop():
    """بدء حلقة الوكيل"""
    return agent_loop.start()

@app.post("/v1/agent-loop/stop")
def stop_agent_loop():
    """إيقاف حلقة الوكيل"""
    return agent_loop.stop()

@app.get("/v1/agent-loop/status")
def agent_loop_status():
    """حالة حلقة الوكيل"""
    return agent_loop.status()

@app.post("/v1/agent-loop/step")
def agent_loop_step():
    """تنفيذ خطوة واحدة من حلقة الوكيل"""
    return agent_loop.step()

# ===== مسارات المعرفة =====
@app.post("/v1/knowledge", response_model=KnowledgeItemOut)
def create_knowledge_item(item: KnowledgeItemCreate, db: Session = Depends(get_db)):
    """إضافة عنصر معرفة جديد"""
    # تحديث المعرفة في الذاكرة
    core_ai.learn(item.key, item.value, item.confidence)

    # حفظ في قاعدة البيانات
    db_item = KnowledgeItem(
        key=item.key,
        value=item.value,
        confidence=item.confidence,
        source=item.source or "manual"
    )
    db.add(db_item)
    db.commit()
    db.refresh(db_item)

    return KnowledgeItemOut(
        id=db_item.id,
        key=db_item.key,
        value=db_item.value,
        confidence=db_item.confidence,
        source=db_item.source,
        created_at=db_item.created_at,
        updated_at=db_item.updated_at
    )

@app.get("/v1/knowledge", response_model=List[KnowledgeItemOut])
def list_knowledge_items(limit: int = 50, db: Session = Depends(get_db)):
    """قائمة عناصر المعرفة"""
    items = db.query(KnowledgeItem).order_by(KnowledgeItem.updated_at.desc()).limit(limit).all()
    return [
        KnowledgeItemOut(
            id=item.id,
            key=item.key,
            value=item.value,
            confidence=item.confidence,
            source=item.source,
            created_at=item.created_at,
            updated_at=item.updated_at
        )
        for item in items
    ]

@app.get("/v1/knowledge/summary")
def knowledge_summary():
    """ملخص المعرفة"""
    return core_ai.get_knowledge_summary()

# ===== WebSocket Endpoints =====
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        # إرسال رسالة ترحيب
        welcome_msg = {
            "type": "connection_established",
            "message": "مرحباً! تم الاتصال بخدمة ويليكس الذكية",
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "ai_status": agent_loop.status()
        }
        await manager.send_personal_message(json.dumps(welcome_msg, ensure_ascii=False), websocket)

        while True:
            # استقبال الرسائل من العميل
            data = await websocket.receive_text()
            try:
                message = json.loads(data)

                if message.get("type") == "voice_input":
                    # معالجة الإدخال الصوتي
                    prompt = message.get("prompt", "")
                    if prompt.strip():
                        # استدعاء نفس منطق /v1/infer
                        ok, msg = policy_router.precheck(prompt)
                        if not ok:
                            error_response = {
                                "type": "error",
                                "message": msg,
                                "timestamp": datetime.utcnow().isoformat() + "Z"
                            }
                            await manager.send_personal_message(json.dumps(error_response, ensure_ascii=False), websocket)
                            continue

                        out = core_ai.think(prompt)
                        out = policy_router.postcheck(out)

                        # تعلم من التفاعل
                        core_ai.learn(f"محادثة_{len(core_ai.knowledge)}", prompt, confidence=0.6)

                        response = {
                            "type": "voice_response",
                            "prompt": prompt,
                            "output": out,
                            "timestamp": datetime.utcnow().isoformat() + "Z",
                            "ai_knowledge_items": len(core_ai.knowledge)
                        }

                        # إرسال للمرسل
                        await manager.send_personal_message(json.dumps(response, ensure_ascii=False), websocket)

                        # بث للجميع إذا كان في وضع البث
                        if message.get("broadcast", False):
                            broadcast_msg = {
                                "type": "broadcast_message",
                                "prompt": prompt,
                                "output": out,
                                "timestamp": datetime.utcnow().isoformat() + "Z"
                            }
                            await manager.broadcast(json.dumps(broadcast_msg, ensure_ascii=False))

                elif message.get("type") == "ping":
                    # رد على ping
                    pong_response = {
                        "type": "pong",
                        "timestamp": datetime.utcnow().isoformat() + "Z",
                        "agent_loop_status": agent_loop.status()
                    }
                    await manager.send_personal_message(json.dumps(pong_response, ensure_ascii=False), websocket)

            except json.JSONDecodeError:
                error_response = {
                    "type": "error",
                    "message": "تنسيق JSON غير صالح",
                    "timestamp": datetime.utcnow().isoformat() + "Z"
                }
                await manager.send_personal_message(json.dumps(error_response, ensure_ascii=False), websocket)

    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        print(f"WebSocket error: {e}")
        manager.disconnect(websocket)

# ===== مسارات قاعدة البيانات =====
@app.post("/v1/agents", response_model=AgentOut)
def create_agent(body: AgentCreate, db: Session = Depends(get_db)):
    exists = db.query(Agent).filter(Agent.code == body.code).first()
    if exists:
        raise HTTPException(status_code=409, detail="Agent code already exists")
    agent = Agent(code=body.code, kind=body.kind)
    db.add(agent)
    db.commit()
    db.refresh(agent)
    return AgentOut(
        id=agent.id, code=agent.code, kind=agent.kind,
        status=agent.status, created_at=agent.created_at
    )

@app.get("/v1/agents", response_model=List[AgentOut])
def list_agents(db: Session = Depends(get_db)):
    rows = db.query(Agent).order_by(Agent.id.desc()).all()
    return [
        AgentOut(
            id=a.id, code=a.code, kind=a.kind,
            status=a.status, created_at=a.created_at
        )
        for a in rows
    ]

@app.post("/v1/tasks", response_model=TaskOut)
def create_task(body: TaskCreate, db: Session = Depends(get_db)):
    # تأكد أن الوكيل موجود
    agent = db.query(Agent).filter(Agent.id == body.agent_id).first()
    if not agent:
        raise HTTPException(status_code=404, detail="Agent not found")

    # تقدير المخاطرة واقتراح أولوية
    risk_score, suggested_priority = score_task_risk(body.title, body.details)
    # لو المرسل حدد أولوية أعلى (رقم أصغر) نحتفظ بها، وإلا نستخدم المقترحة
    priority_final = min(body.priority or 3, suggested_priority)

    task = Task(
        agent_id=body.agent_id,
        title=body.title,
        details=body.details,
        priority=priority_final,
        risk_score=risk_score
    )
    db.add(task)
    db.commit()
    db.refresh(task)

    # (اختياري) تخزين التقدير داخل details لسهولة العرض
    try:
        d = task.details or {}
        d["_risk_score"] = risk_score
        d["_priority_suggested"] = suggested_priority
        task.details = d
        db.add(task); db.commit(); db.refresh(task)
    except Exception:
        pass

    # تعلم من نوع المهمة الجديدة
    core_ai.learn(
        f"نوع_مهمة_{task.priority}",
        f"مهمة بعنوان: {task.title[:50]}... مع مخاطرة {risk_score}",
        confidence=0.5
    )

    return TaskOut(**{
        "id": task.id, "agent_id": task.agent_id, "title": task.title,
        "details": task.details, "priority": task.priority, "risk_score": task.risk_score,
        "state": task.state, "created_at": task.created_at
    })

@app.get("/v1/tasks", response_model=List[TaskOut])
def list_tasks(agent_id: Optional[int] = None, db: Session = Depends(get_db)):
    q = db.query(Task)
    if agent_id is not None:
        q = q.filter(Task.agent_id == agent_id)
    rows = q.order_by(Task.id.desc()).all()
    return [
        TaskOut(
            id=t.id, agent_id=t.agent_id, title=t.title,
            details=t.details, priority=t.priority, risk_score=t.risk_score,
            state=t.state, created_at=t.created_at
        )
        for t in rows
    ]

# ===== مسارات تحديث حالة المهام وسجلاتها =====
@app.patch("/v1/tasks/{task_id}/state")
def update_task_state(task_id: int, body: TaskStateUpdate, db: Session = Depends(get_db)):
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    prev = task.state
    task.state = body.to_state
    db.add(task)
    # سجل التغيير
    log = TaskLog(task_id=task_id, from_state=prev, to_state=body.to_state, note=body.note)
    db.add(log)
    db.commit()

    # تعلم من تغيير الحالة
    core_ai.learn(
        f"تغيير_حالة_{prev}_إلى_{body.to_state}",
        f"مهمة {task_id}: {task.title[:30]}...",
        confidence=0.7
    )

    return {"ok": True, "task_id": task_id, "from": prev, "to": body.to_state}

@app.get("/v1/tasks/{task_id}/logs")
def list_task_logs(task_id: int, db: Session = Depends(get_db)):
    rows = db.query(TaskLog).filter(TaskLog.task_id == task_id).order_by(TaskLog.id.desc()).all()
    return [{
        "id": r.id, "task_id": r.task_id, "from_state": r.from_state,
        "to_state": r.to_state, "note": r.note, "created_at": r.created_at
    } for r in rows]

# ===== مسار نبض الوكلاء =====
@app.post("/v1/agents/{agent_id}/heartbeat")
def agent_heartbeat(agent_id: int, body: AgentHeartbeat, db: Session = Depends(get_db)):
    agent = db.query(Agent).filter(Agent.id == agent_id).first()
    if not agent:
        raise HTTPException(status_code=404, detail="Agent not found")

    # مبدئيًا نضيف رسالة في جدول messages لأرشفة النبض
    m = Message(agent_id=agent.id, role="SYSTEM", content="HEARTBEAT", meta=body.meta or {})
    db.add(m)

    if body.set_status:
        agent.status = body.set_status
        db.add(agent)

    db.commit()
    return {"ok": True, "agent_id": agent.id, "status": agent.status}

# ===== نقطة نهاية تقدير المخاطرة =====
@app.post("/v1/assess-risk", response_model=RiskAssessmentResponse)
def assess_task_risk(req: RiskAssessmentRequest):
    """تقدير مخاطرة المهمة بدون إنشائها"""
    risk_score, suggested_priority = score_task_risk(req.title, req.details)

    # تحديد مستوى المخاطرة
    if risk_score >= 40:
        risk_level = "عالي"
    elif risk_score >= 15:
        risk_level = "متوسط"
    else:
        risk_level = "منخفض"

    # العثور على الكلمات المكتشفة
    text = (req.title or "")
    if req.details:
        try:
            import json
            text += " " + json.dumps(req.details, ensure_ascii=False)
        except Exception:
            pass
    text_norm = text.lower()

    detected_keywords = []
    for kw in HIGH_KEYWORDS:
        if kw in text_norm:
            detected_keywords.append(kw)
    for kw in MED_KEYWORDS:
        if kw in text_norm:
            detected_keywords.append(kw)

    # تعلم من تقدير المخاطرة
    core_ai.learn(
        f"تقدير_مخاطرة_{risk_level}",
        f"مهمة: {req.title[:30]}... = {risk_score} نقطة",
        confidence=0.6
    )

    return RiskAssessmentResponse(
        risk_score=risk_score,
        suggested_priority=suggested_priority,
        risk_level=risk_level,
        detected_keywords=detected_keywords
    )

if __name__ == "__main__":
    # تحميل المعرفة المحفوظة من قاعدة البيانات عند البدء
    try:
        with SessionLocal() as db:
            knowledge_items = db.query(KnowledgeItem).all()
            for item in knowledge_items:
                core_ai.learn(item.key, item.value, item.confidence)
            print(f"✅ تم تحميل {len(knowledge_items)} عنصر معرفة من قاعدة البيانات")
    except Exception as e:
        print(f"⚠️ تحذير: لا يمكن تحميل المعرفة من قاعدة البيانات: {e}")

    # شغّل محليًا على 8080 (HTTP). لو ترغب SSL استخدم أوامر uvicorn من الطرفية
    uvicorn.run("main:app", host="0.0.0.0", port=8080, reload=True)
