import os
import sys
import asyncio
from pathlib import Path
from typing import List, Optional, Tuple, Dict, Any
from datetime import datetime
import random

import uvicorn
import yaml
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

# ===== ربط Policy SDK =====
sys.path.append(str((Path(__file__).resolve().parents[2] / "libs")))
from policy_sdk.policy import PolicyRouter  # type: ignore

# ===== قاعدة البيانات (SQLAlchemy) =====
from sqlalchemy import (
    create_engine, text, String, Enum, Text, JSON,
    TIMESTAMP, ForeignKey, Integer, BigInteger, select
)
from sqlalchemy.orm import (
    sessionmaker, DeclarativeBase, Mapped, mapped_column, Session
)

# -------------------------------
# إعدادات الاتصال بقاعدة البيانات
# -------------------------------
DB_URL = os.environ.get(
    "DB_URL",
    "mysql+pymysql://weilix_user:StrongPass!123@127.0.0.1:3306/weilix_db"
)
engine = create_engine(DB_URL, pool_pre_ping=True, future=True)
SessionLocal = sessionmaker(bind=engine, autoflush=False, autocommit=False, future=True)

class Base(DeclarativeBase):
    pass

# ========== جداول ORM ==========
class Agent(Base):
    __tablename__ = "agents"
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    code: Mapped[str] = mapped_column(String(64), unique=True, nullable=False)  # anat-1 / inana-3
    kind: Mapped[str] = mapped_column(Enum("ANAT", "INANA", name="agent_kind"), nullable=False)
    status: Mapped[str] = mapped_column(Enum("ACTIVE", "PAUSED", "RETIRED", name="agent_status"), default="ACTIVE")
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow)

class Task(Base):
    __tablename__ = "tasks"
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    agent_id: Mapped[int] = mapped_column(BigInteger, ForeignKey("agents.id"), nullable=False)
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    details: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    priority: Mapped[int] = mapped_column(Integer, default=2)  # 1 عالي / 2 عادي / 3 منخفض
    state: Mapped[str] = mapped_column(Enum("QUEUED", "RUNNING", "DONE", "FAILED", name="task_state"), default="QUEUED")
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow)

class Message(Base):
    __tablename__ = "messages"
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    agent_id: Mapped[Optional[int]] = mapped_column(BigInteger, ForeignKey("agents.id"), nullable=True)
    role: Mapped[str] = mapped_column(Enum("USER", "WEILIX", "SYSTEM", name="msg_role"), nullable=False)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    meta: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow)

class PolicyDoc(Base):
    __tablename__ = "policies"
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    policy_id: Mapped[str] = mapped_column(String(128), nullable=False)
    version: Mapped[str] = mapped_column(String(32), nullable=False)
    body: Mapped[str] = mapped_column(Text, nullable=False)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow)

class AuditLog(Base):
    __tablename__ = "audit_logs"
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    event: Mapped[str] = mapped_column(String(64), nullable=False)
    payload: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    prev_hash: Mapped[Optional[str]] = mapped_column(String(64), nullable=True)
    hash: Mapped[str] = mapped_column(String(64), nullable=False)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow)

# سجلات تغيّر حالة المهام
class TaskLog(Base):
    __tablename__ = "task_logs"
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    task_id: Mapped[int] = mapped_column(BigInteger, ForeignKey("tasks.id"), nullable=False)
    from_state: Mapped[Optional[str]] = mapped_column(String(16), nullable=True)
    to_state: Mapped[str] = mapped_column(String(16), nullable=False)
    note: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow)

# مخزن معرفة بسيط
class KnowledgeItem(Base):
    __tablename__ = "knowledge_items"
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    key: Mapped[str] = mapped_column(String(128), nullable=False)
    value: Mapped[dict] = mapped_column(JSON, nullable=False)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow)

# حالة/إعدادات نموذج قابلة للتطوّر
class ModelState(Base):
    __tablename__ = "model_state"
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(64), unique=True, nullable=False)
    data: Mapped[dict] = mapped_column(JSON, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow)

# إنشاء الجداول إن لم تكن موجودة
Base.metadata.create_all(bind=engine)

# تبعية جلسة DB
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# ===== إعداد التطبيق/السياسات =====
APP_VERSION = "weilix-core-v3"
CONSTITUTION_PATH = os.environ.get(
    "CONSTITUTION",
    str(Path(__file__).resolve().parents[2] / "configs" / "constitution.yaml")
)
with open(CONSTITUTION_PATH, "r", encoding="utf-8") as f:
    POLICY_DOC = yaml.safe_load(f)
policy_router = PolicyRouter(POLICY_DOC)

LAST_HASH = ""

# ===== نماذج API =====
class InferReq(BaseModel):
    prompt: str
    context: List[str] = Field(default_factory=list)

class InferRes(BaseModel):
    output: str
    model_version: str
    audit_hash: str
    model_config = {"protected_namespaces": ()}

class AgentCreate(BaseModel):
    code: str = Field(..., examples=["anat-1"])
    kind: str = Field(..., pattern="^(ANAT|INANA)$")

class AgentOut(BaseModel):
    id: int
    code: str
    kind: str
    status: str
    created_at: datetime

class TaskCreate(BaseModel):
    agent_id: int
    title: str
    details: Optional[dict] = None
    priority: int = 2

class TaskOut(BaseModel):
    id: int
    agent_id: int
    title: str
    details: Optional[dict]
    priority: int
    state: str
    created_at: datetime

class TaskStateUpdate(BaseModel):
    to_state: str = Field(..., pattern="^(QUEUED|RUNNING|DONE|FAILED)$")
    note: Optional[str] = None

# ===== Core AI (بدائية ويمكن تطويرها لاحقًا) =====
class CoreAI:
    def __init__(self):
        self.memory: List[Dict[str, Any]] = []
        self.knowledge: Dict[str, Any] = {}

    def think(self, prompt: str) -> str:
        self.memory.append({"ts": datetime.utcnow().isoformat()+"Z", "in": prompt})
        p = prompt.strip()
        if not p:
            out = "قل لي ماذا تريد أن أفعل."
        elif "مرحبا" in p or "أهلاً" in p or "hello" in p.lower():
            out = "أهلاً بك، كيف أخدمك اليوم؟"
        elif "مهمة" in p:
            out = "تم تسجيل المهمة في النظام. جارٍ المعالجة..."
        else:
            out = random.choice([
                "أفكر فيما قلته… تابع.",
                "مثير للاهتمام، أعطني تفاصيل أكثر.",
                "سأحاول التعلم من ذلك وتطوير نفسي."
            ])
        self.memory.append({"ts": datetime.utcnow().isoformat()+"Z", "out": out})
        return out

    def learn(self, feedback: str):
        k = f"exp_{len(self.knowledge)+1}"
        self.knowledge[k] = {"feedback": feedback, "time": datetime.utcnow().isoformat()+"Z"}

core_ai = CoreAI()

# ==== تقدير مخاطرة (مهام) ====
HIGH_KEYWORDS = {"اختراق","تعطيل","تجسس","ddos","هجوم","priv-esc","root","malware","phishing"}
MED_KEYWORDS  = {"تحذير","ثغرة","leak","bruteforce","anomaly","مشبوه","suspicious"}

def score_task_risk(title: str, details: dict | None) -> Tuple[int, int]:
    text = (title or "")
    if details:
        try:
            import json
            text += " " + json.dumps(details, ensure_ascii=False)
        except Exception:
            pass
    text_norm = text.lower()
    score = 0
    for kw in HIGH_KEYWORDS:
        if kw in text_norm:
            score += 25
    for kw in MED_KEYWORDS:
        if kw in text_norm:
            score += 10
    score = max(0, min(100, score))
    pr = 1 if score >= 40 else (2 if score >= 15 else 3)
    return score, pr

# ===== Agent Loop =====
class AgentLoop:
    """
    حلقة ذاتية: تخطيط → تنفيذ → مراجعة → تعلم.
    - تختار أول مهمّة QUEUED (حسب الأولوية ثم الأقدم).
    - تغيّر الحالة إلى RUNNING ثم DONE/FAILED.
    - تخزن TaskLog وتضيف خبرة بسيطة في KnowledgeItem.
    - لا تقوم بأي عمليات غير قانونية — كل شيء يبقى داخل النظام.
    """
    def __init__(self, session_factory: sessionmaker, policy: PolicyRouter):
        self.sf = session_factory
        self.policy = policy
        self._running = False
        self._tick = 0
        self._last_result: Optional[dict] = None

    def status(self) -> dict:
        return {
            "running": self._running,
            "tick": self._tick,
            "last_result": self._last_result or {}
        }

    def _choose_task(self, db: Session) -> Optional[Task]:
        q = db.query(Task).filter(Task.state == "QUEUED").order_by(Task.priority.asc(), Task.id.asc())
        return q.first()

    def _execute(self, title: str, details: Optional[dict]) -> Tuple[str, str, dict]:
        """
        تنفيذ افتراضي آمن (محاكاة).
        يرجع: (status, message, meta)
        """
        # تمرير عبر السياسة لقطع أي شيء غير مسموح
        ok, msg = self.policy.precheck(title)
        if not ok:
            return "FAILED", f"policy_precheck_blocked: {msg}", {"policy": "blocked"}

        # مثال تنفيذ: ردّ مركّب بناءً على كلمات مفتاحية
        risk, suggested = score_task_risk(title, details or {})
        narrative = []
        narrative.append(f"تنفيذ مهمة: {title}")
        narrative.append(f"خطر تقديري: {risk} (أولوية مقترحة: {suggested})")
        if details:
            narrative.append(f"تفاصيل: {details}")

        # محاكاة نتيجة
        success = risk < 80  # كلما زاد الخطر احتمال الفشل أعلى
        status = "DONE" if success else "FAILED"
        narrative.append("النتيجة: " + ("تم التنفيذ بنجاح." if success else "تعذّر التنفيذ في المحاكاة."))

        meta = {
            "risk_score": risk,
            "priority_suggested": suggested,
            "exec_time_ms": random.randint(30, 300),
        }
        return status, "\n".join(narrative), meta

    def _reflect_and_learn(self, db: Session, task: Task, exec_meta: dict, message: str):
        # حفظ خبرة مبسطة
        ki = KnowledgeItem(
            key=f"task_{task.id}",
            value={
                "title": task.title,
                "ts": datetime.utcnow().isoformat() + "Z",
                "meta": exec_meta,
                "summary": message[:800]
            }
        )
        db.add(ki)

        # تعديل حالة (model_state) بسيطة: عدد المهام/المتوسطات
        ms = db.execute(select(ModelState).where(ModelState.name == "loop_stats")).scalar_one_or_none()
        if not ms:
            ms = ModelState(name="loop_stats", data={"done": 0, "failed": 0, "avg_risk": 0.0})
            db.add(ms); db.flush()
        d = ms.data
        if task.state == "DONE":
            d["done"] = int(d.get("done", 0)) + 1
        elif task.state == "FAILED":
            d["failed"] = int(d.get("failed", 0)) + 1
        # محدث المتوسط
        r = exec_meta.get("risk_score", 0)
        n = max(1, d.get("done", 0) + d.get("failed", 0))
        d["avg_risk"] = round(((d.get("avg_risk", 0.0) * (n - 1)) + r) / n, 2)
        ms.data = d
        db.add(ms)

    async def step(self) -> dict:
        """تنفيذ خطوة واحدة من الحلقة"""
        with self.sf() as db:
            task = self._choose_task(db)
            if not task:
                self._last_result = {"info": "no_queued_tasks"}
                return {"ok": True, "info": "no_queued_tasks"}

            prev = task.state
            task.state = "RUNNING"
            db.add(task)
            db.add(TaskLog(task_id=task.id, from_state=prev, to_state="RUNNING", note="agent_loop:start"))
            db.commit()

            # تنفيذ (محاكاة آمنة)
            status, message, meta = self._execute(task.title, task.details or {})

            prev2 = task.state
            task.state = status
            # تحديث details بإضافة أثر التنفيذ
            d = (task.details or {}).copy()
            d["_exec"] = {"at": datetime.utcnow().isoformat()+"Z", "meta": meta}
            task.details = d
            db.add(task)
            db.add(TaskLog(task_id=task.id, from_state=prev2, to_state=status, note=message[:512]))
            db.add(Message(agent_id=task.agent_id, role="SYSTEM", content="TASK_"+status, meta={"task_id": task.id, **meta}))
            self._reflect_and_learn(db, task, meta, message)
            db.commit()

            self._tick += 1
            self._last_result = {"task_id": task.id, "status": status, "meta": meta}
            return {"ok": True, "task_id": task.id, "status": status, "meta": meta}

    async def run_forever(self, interval_sec: float, stop_event: asyncio.Event):
        """تشغيل متواصل حتى طلب الإيقاف"""
        self._running = True
        try:
            while not stop_event.is_set():
                await self.step()
                await asyncio.sleep(interval_sec)
        finally:
            self._running = False

agent_loop = AgentLoop(SessionLocal, policy_router)
_runner_task: Optional[asyncio.Task] = None
_stop_event: Optional[asyncio.Event] = None

# ===== تطبيق FastAPI =====
app = FastAPI(title="Weilix API", version=APP_VERSION)

# ملاحظة: لا يجوز set allow_credentials=True مع "*" لذا جعلناها False
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://127.0.0.1:8080",
        "http://localhost:8080",
        "https://127.0.0.1:8443",
        "https://localhost:8443",
        "*",
    ],
    allow_credentials=False,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ===== مسارات النظام =====
@app.get("/v1/health")
def health():
    return {"status": "ok", "version": APP_VERSION}

@app.get("/v1/db-ping")
def db_ping():
    try:
        with engine.connect() as conn:
            r = conn.execute(text("SELECT 1")).scalar()
            return {"db": "ok" if r == 1 else "fail"}
    except Exception as e:
        return {"db": "error", "detail": str(e)}

# ===== مسارات الذكاء (نص) =====
@app.post("/v1/infer", response_model=InferRes)
def infer(req: InferReq):
    global LAST_HASH
    ok, msg = policy_router.precheck(req.prompt)
    if not ok:
        raise HTTPException(status_code=400, detail=msg)

    out = core_ai.think(req.prompt)
    out = policy_router.postcheck(out)

    record = {
        "ts": datetime.utcnow().isoformat() + "Z",
        "endpoint": "/v1/infer",
        "input_len": len(req.prompt),
        "output_len": len(out),
        "version": APP_VERSION,
    }
    LAST_HASH = policy_router.hash_chain(LAST_HASH, record)
    return InferRes(output=out, model_version=APP_VERSION, audit_hash=LAST_HASH)

@app.post("/v1/evolve")
def evolve():
    # مكان مخصص لتشغيل عمليات تعلم مجمّعة لاحقًا
    return {"status": "scheduled", "version": APP_VERSION}

# ===== مسارات الوكلاء =====
@app.post("/v1/agents", response_model=AgentOut)
def create_agent(body: AgentCreate, db: Session = Depends(get_db)):
    exists = db.query(Agent).filter(Agent.code == body.code).first()
    if exists:
        raise HTTPException(status_code=409, detail="Agent code already exists")
    agent = Agent(code=body.code, kind=body.kind)
    db.add(agent); db.commit(); db.refresh(agent)
    return AgentOut(
        id=agent.id, code=agent.code, kind=agent.kind,
        status=agent.status, created_at=agent.created_at
    )

@app.get("/v1/agents", response_model=List[AgentOut])
def list_agents(db: Session = Depends(get_db)):
    rows = db.query(Agent).order_by(Agent.id.desc()).all()
    return [
        AgentOut(id=a.id, code=a.code, kind=a.kind, status=a.status, created_at=a.created_at)
        for a in rows
    ]

class AgentHeartbeat(BaseModel):
    meta: Optional[dict] = None
    set_status: Optional[str] = Field(None, pattern="^(ACTIVE|PAUSED|RETIRED)$")

@app.post("/v1/agents/{agent_id}/heartbeat")
def agent_heartbeat(agent_id: int, body: AgentHeartbeat, db: Session = Depends(get_db)):
    agent = db.query(Agent).filter(Agent.id == agent_id).first()
    if not agent:
        raise HTTPException(status_code=404, detail="Agent not found")
    m = Message(agent_id=agent.id, role="SYSTEM", content="HEARTBEAT", meta=body.meta or {})
    db.add(m)
    if body.set_status:
        agent.status = body.set_status
        db.add(agent)
    db.commit()
    return {"ok": True, "agent_id": agent.id, "status": agent.status}

# ===== مسارات المهام =====
@app.post("/v1/tasks", response_model=TaskOut)
def create_task(body: TaskCreate, db: Session = Depends(get_db)):
    agent = db.query(Agent).filter(Agent.id == body.agent_id).first()
    if not agent:
        raise HTTPException(status_code=404, detail="Agent not found")
    risk_score, suggested_priority = score_task_risk(body.title, body.details)
    priority_final = min(body.priority or 3, suggested_priority)
    task = Task(
        agent_id=body.agent_id, title=body.title,
        details=(body.details or {}) | {"_risk_score": risk_score, "_priority_suggested": suggested_priority},
        priority=priority_final
    )
    db.add(task); db.commit(); db.refresh(task)
    return TaskOut(
        id=task.id, agent_id=task.agent_id, title=task.title,
        details=task.details, priority=task.priority,
        state=task.state, created_at=task.created_at
    )

@app.get("/v1/tasks", response_model=List[TaskOut])
def list_tasks(agent_id: Optional[int] = None, db: Session = Depends(get_db)):
    q = db.query(Task)
    if agent_id is not None:
        q = q.filter(Task.agent_id == agent_id)
    rows = q.order_by(Task.id.desc()).all()
    return [
        TaskOut(
            id=t.id, agent_id=t.agent_id, title=t.title,
            details=t.details, priority=t.priority,
            state=t.state, created_at=t.created_at
        )
        for t in rows
    ]

@app.patch("/v1/tasks/{task_id}/state")
def update_task_state(task_id: int, body: TaskStateUpdate, db: Session = Depends(get_db)):
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    prev = task.state
    task.state = body.to_state
    db.add(task)
    db.add(TaskLog(task_id=task_id, from_state=prev, to_state=body.to_state, note=body.note))
    db.commit()
    return {"ok": True, "task_id": task_id, "from": prev, "to": body.to_state}

@app.get("/v1/tasks/{task_id}/logs")
def list_task_logs(task_id: int, db: Session = Depends(get_db)):
    rows = db.query(TaskLog).filter(TaskLog.task_id == task_id).order_by(TaskLog.id.desc()).all()
    return [{
        "id": r.id, "task_id": r.task_id, "from_state": r.from_state,
        "to_state": r.to_state, "note": r.note, "created_at": r.created_at
    } for r in rows]

# ===== مسارات تشغيل حلقة الذكاء =====
class LoopStartReq(BaseModel):
    interval_ms: int = 1000

@app.get("/v1/loop/status")
def loop_status():
    return agent_loop.status()

@app.post("/v1/loop/step")
async def loop_step():
    res = await agent_loop.step()
    return {"status": "ok", **res}

@app.post("/v1/loop/start")
async def loop_start(body: LoopStartReq):
    global _runner_task, _stop_event
    if _runner_task and not _runner_task.done():
        return {"running": True, "note": "already running"}
    _stop_event = asyncio.Event()
    _runner_task = asyncio.create_task(agent_loop.run_forever(max(0.05, body.interval_ms/1000), _stop_event))
    return {"running": True, "interval_ms": body.interval_ms}

@app.post("/v1/loop/stop")
async def loop_stop():
    global _runner_task, _stop_event
    if not _runner_task:
        return {"running": False}
    if _stop_event:
        _stop_event.set()
    try:
        await asyncio.wait_for(_runner_task, timeout=3.0)
    except asyncio.TimeoutError:
        _runner_task.cancel()
    _runner_task = None
    return {"running": False}

# ===== التشغيل المحلي =====
if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8080, reload=True)
