<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض توضيحي - ويليكس الذكي متعدد اللغات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .feature-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
        }
        
        .feature-icon {
            font-size: 4em;
            margin-bottom: 20px;
        }
        
        .feature-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .feature-description {
            color: #666;
            line-height: 1.6;
        }
        
        .demo-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 40px;
        }
        
        .demo-title {
            text-align: center;
            font-size: 2em;
            margin-bottom: 30px;
            color: #333;
        }
        
        .language-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .language-example {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .language-example:hover {
            border-color: #4facfe;
            background: #e3f2fd;
            transform: scale(1.02);
        }
        
        .language-flag {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .language-name {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .language-text {
            color: #666;
            font-style: italic;
        }
        
        .cta-section {
            text-align: center;
            margin-top: 40px;
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px 40px;
            border-radius: 50px;
            text-decoration: none;
            font-size: 1.2em;
            font-weight: bold;
            box-shadow: 0 10px 30px rgba(79, 172, 254, 0.3);
            transition: all 0.3s ease;
        }
        
        .cta-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(79, 172, 254, 0.4);
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 30px;
            margin-top: 40px;
            color: white;
        }
        
        .stat {
            text-align: center;
        }
        
        .stat-number {
            font-size: 3em;
            font-weight: bold;
            display: block;
        }
        
        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 ويليكس الذكي</h1>
            <p>المساعد الذكي متعدد اللغات مع كشف اللغة التلقائي</p>
        </div>
        
        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">🎤</div>
                <div class="feature-title">كشف اللغة التلقائي</div>
                <div class="feature-description">
                    اضغط على زر الميكروفون وتحدث بأي لغة - ويليكس سيكتشفها تلقائياً ويجيبك بنفس اللغة
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🌍</div>
                <div class="feature-title">19 لغة مدعومة</div>
                <div class="feature-description">
                    من العربية إلى اليابانية، من الإنجليزية إلى الصينية - ويليكس يفهم ويتحدث بـ 19 لغة مختلفة
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🧠</div>
                <div class="feature-title">ذكاء متطور</div>
                <div class="feature-description">
                    يتعلم من كل محادثة ويطور معرفته باستمرار مع نظام AgentLoop المتقدم
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔊</div>
                <div class="feature-title">قراءة صوتية</div>
                <div class="feature-description">
                    يقرأ الردود بالصوت المناسب للغة المكتشفة مع دعم الأصوات الطبيعية
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">🗣️ جرب التحدث بأي من هذه اللغات</div>
            <div class="language-examples">
                <div class="language-example" onclick="openChat()">
                    <div class="language-flag">🇸🇦</div>
                    <div class="language-name">العربية</div>
                    <div class="language-text">"مرحبا ويليكس، كيف حالك؟"</div>
                </div>
                
                <div class="language-example" onclick="openChat()">
                    <div class="language-flag">🇺🇸</div>
                    <div class="language-name">English</div>
                    <div class="language-text">"Hello Weilix, how are you?"</div>
                </div>
                
                <div class="language-example" onclick="openChat()">
                    <div class="language-flag">🇫🇷</div>
                    <div class="language-name">Français</div>
                    <div class="language-text">"Bonjour Weilix, comment allez-vous?"</div>
                </div>
                
                <div class="language-example" onclick="openChat()">
                    <div class="language-flag">🇪🇸</div>
                    <div class="language-name">Español</div>
                    <div class="language-text">"Hola Weilix, ¿cómo estás?"</div>
                </div>
                
                <div class="language-example" onclick="openChat()">
                    <div class="language-flag">🇩🇪</div>
                    <div class="language-name">Deutsch</div>
                    <div class="language-text">"Hallo Weilix, wie geht es dir?"</div>
                </div>
                
                <div class="language-example" onclick="openChat()">
                    <div class="language-flag">🇯🇵</div>
                    <div class="language-name">日本語</div>
                    <div class="language-text">"こんにちは、元気ですか？"</div>
                </div>
                
                <div class="language-example" onclick="openChat()">
                    <div class="language-flag">🇨🇳</div>
                    <div class="language-name">中文</div>
                    <div class="language-text">"你好，你好吗？"</div>
                </div>
                
                <div class="language-example" onclick="openChat()">
                    <div class="language-flag">🇷🇺</div>
                    <div class="language-name">Русский</div>
                    <div class="language-text">"Привет, как дела?"</div>
                </div>
            </div>
        </div>
        
        <div class="stats">
            <div class="stat">
                <span class="stat-number">19</span>
                <span class="stat-label">لغة مدعومة</span>
            </div>
            <div class="stat">
                <span class="stat-number">100%</span>
                <span class="stat-label">كشف تلقائي</span>
            </div>
            <div class="stat">
                <span class="stat-number">∞</span>
                <span class="stat-label">تعلم مستمر</span>
            </div>
        </div>
        
        <div class="cta-section">
            <a href="chat.html" class="cta-button">
                🚀 ابدأ المحادثة الآن
            </a>
        </div>
    </div>

    <script>
        function openChat() {
            window.open('chat.html', '_blank');
        }
        
        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.feature-card, .language-example');
            
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>
