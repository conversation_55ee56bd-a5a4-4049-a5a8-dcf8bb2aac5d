# Weilix MVP v2 - إعداد البيئة (PowerShell)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "إعداد بيئة Weilix MVP v2" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# التحقق من وجود Python
try {
    $pythonVersion = python --version 2>$null
    Write-Host "✓ تم العثور على Python: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "خطأ: Python غير مثبت أو غير موجود في PATH" -ForegroundColor Red
    Write-Host "يرجى تثبيت Python 3.11 أو أحدث" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# إنشاء البيئة الافتراضية إذا لم تكن موجودة
if (-not (Test-Path "venv")) {
    Write-Host "إنشاء البيئة الافتراضية..." -ForegroundColor Yellow
    python -m venv venv
    if ($LASTEXITCODE -ne 0) {
        Write-Host "خطأ في إنشاء البيئة الافتراضية" -ForegroundColor Red
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
    Write-Host "✓ تم إنشاء البيئة الافتراضية" -ForegroundColor Green
} else {
    Write-Host "✓ البيئة الافتراضية موجودة" -ForegroundColor Green
}

# تفعيل البيئة الافتراضية
Write-Host "تفعيل البيئة الافتراضية..." -ForegroundColor Yellow
& ".\venv\Scripts\Activate.ps1"

# تحديث pip
Write-Host "تحديث pip..." -ForegroundColor Yellow
python -m pip install --upgrade pip

# الانتقال إلى مجلد services/core
Set-Location "services\core"

# تثبيت المتطلبات
Write-Host "تثبيت المتطلبات من requirements.txt..." -ForegroundColor Yellow
pip install -r requirements.txt
if ($LASTEXITCODE -ne 0) {
    Write-Host "خطأ في تثبيت المتطلبات" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host "✓ تم تثبيت جميع المتطلبات بنجاح" -ForegroundColor Green

# العودة إلى المجلد الرئيسي
Set-Location "..\..\"

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "تم إعداد البيئة بنجاح!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "لتشغيل المشروع:" -ForegroundColor Yellow
Write-Host "1. تفعيل البيئة: .\venv\Scripts\Activate.ps1" -ForegroundColor White
Write-Host "2. الانتقال للمجلد: cd services\core" -ForegroundColor White
Write-Host "3. تشغيل الخادم: uvicorn main:app --reload --port 8080" -ForegroundColor White
Write-Host ""
Write-Host "أو استخدم: docker-compose up" -ForegroundColor White
Write-Host "========================================" -ForegroundColor Cyan

Read-Host "اضغط Enter للخروج"
