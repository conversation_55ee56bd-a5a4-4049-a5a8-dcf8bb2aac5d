@echo off
echo ========================================
echo إعداد التطبيق الذكي
echo ========================================

REM التحقق من Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت
    pause
    exit /b 1
)

echo ✓ Python موجود

REM إنشاء البيئة الافتراضية
if not exist "venv" (
    echo إنشاء البيئة الافتراضية...
    python -m venv venv
)

REM تفعيل البيئة
call venv\Scripts\activate.bat

REM تثبيت المتطلبات
echo تثبيت المكتبات...
pip install fastapi uvicorn[standard] pydantic

echo ========================================
echo تم الإعداد بنجاح!
echo ========================================
echo.
echo لتشغيل التطبيق:
echo 1. venv\Scripts\activate
echo 2. python main.py
echo.
echo ثم افتح: http://localhost:8000
echo ========================================
pause
