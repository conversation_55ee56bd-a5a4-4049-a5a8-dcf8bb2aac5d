<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيقي الذكي</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        
        .chat-container {
            padding: 30px;
        }
        
        .chat-history {
            height: 400px;
            overflow-y: auto;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            background: #fafafa;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 10px;
            max-width: 80%;
        }
        
        .user-message {
            background: #667eea;
            color: white;
            margin-left: auto;
            text-align: left;
        }
        
        .ai-message {
            background: #e8f4f8;
            color: #333;
            border-left: 4px solid #667eea;
        }
        
        .input-container {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .message-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            font-family: inherit;
        }
        
        .message-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .send-button {
            padding: 15px 30px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        
        .send-button:hover {
            background: #5a6fd8;
        }
        
        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .quick-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .quick-btn {
            padding: 8px 15px;
            background: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .quick-btn:hover {
            background: #667eea;
            color: white;
        }
        
        .status {
            text-align: center;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .status.connected {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .loading {
            display: none;
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 تطبيقي الذكي</h1>
            <p>مساعد ذكي بسيط ومفيد</p>
        </div>
        
        <div class="chat-container">
            <div id="status" class="status">جاري التحقق من الاتصال...</div>
            
            <div class="chat-history" id="chatHistory">
                <div class="ai-message">
                    مرحباً! أنا مساعدك الذكي. كيف يمكنني مساعدتك اليوم؟
                </div>
            </div>
            
            <div class="quick-buttons">
                <button class="quick-btn" onclick="sendQuickMessage('مرحبا')">مرحبا</button>
                <button class="quick-btn" onclick="sendQuickMessage('كيف حالك؟')">كيف حالك؟</button>
                <button class="quick-btn" onclick="sendQuickMessage('ما اسمك؟')">ما اسمك؟</button>
                <button class="quick-btn" onclick="clearHistory()">مسح المحادثة</button>
            </div>
            
            <div class="input-container">
                <input 
                    type="text" 
                    id="messageInput" 
                    class="message-input" 
                    placeholder="اكتب رسالتك هنا..."
                    onkeypress="handleKeyPress(event)"
                >
                <button id="sendButton" class="send-button" onclick="sendMessage()">
                    إرسال
                </button>
            </div>
            
            <div id="loading" class="loading">
                ⏳ جاري المعالجة...
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        // فحص الاتصال عند تحميل الصفحة
        window.onload = checkConnection;
        
        async function checkConnection() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    showStatus('connected', '✅ متصل - ' + data.message);
                } else {
                    throw new Error('فشل في الاتصال');
                }
            } catch (error) {
                showStatus('error', '❌ غير متصل - تأكد من تشغيل الخادم على المنفذ 8000');
            }
        }
        
        function showStatus(type, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${type}`;
            statusEl.textContent = message;
        }
        
        function addMessage(content, isUser = false) {
            const chatHistory = document.getElementById('chatHistory');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'ai-message'}`;
            messageDiv.textContent = content;
            chatHistory.appendChild(messageDiv);
            chatHistory.scrollTop = chatHistory.scrollHeight;
        }
        
        function setLoading(isLoading) {
            const loadingEl = document.getElementById('loading');
            const sendButton = document.getElementById('sendButton');
            
            if (isLoading) {
                loadingEl.style.display = 'block';
                sendButton.disabled = true;
                sendButton.textContent = 'جاري الإرسال...';
            } else {
                loadingEl.style.display = 'none';
                sendButton.disabled = false;
                sendButton.textContent = 'إرسال';
            }
        }
        
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // إضافة رسالة المستخدم
            addMessage(message, true);
            input.value = '';
            
            setLoading(true);
            
            try {
                const response = await fetch(`${API_BASE}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        context: []
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const data = await response.json();
                addMessage(data.response);
                
            } catch (error) {
                addMessage(`خطأ: ${error.message}`, false);
                console.error('Error:', error);
            } finally {
                setLoading(false);
            }
        }
        
        function sendQuickMessage(message) {
            document.getElementById('messageInput').value = message;
            sendMessage();
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        async function clearHistory() {
            try {
                await fetch(`${API_BASE}/history`, { method: 'DELETE' });
                document.getElementById('chatHistory').innerHTML = `
                    <div class="ai-message">
                        تم مسح المحادثة. كيف يمكنني مساعدتك؟
                    </div>
                `;
            } catch (error) {
                console.error('Error clearing history:', error);
            }
        }
    </script>
</body>
</html>
